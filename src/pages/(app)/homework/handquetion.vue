<template>
  <div class="mian">
    <back :url="'/homework/create'" />
    <div class="top">
      <div class="title">计算机网络-作业-{{ homeworkDetails.name }}</div>
    </div>

    <div class="content flex items-center justify-between">
      <div class="name">
        <div style="margin-bottom: 20px;">作业编号：{{ homeworkDetails.id }}</div>
        <div class="setting">
          <span>乱序设置：</span>
          <a-radio-group v-model:value="orderValue">
            <a-radio :value="1">试题乱序</a-radio>
            <a-radio :value="2">选项乱序</a-radio>
          </a-radio-group>
        </div>
      </div>
      <div>
        <a-button type="text" class="fabu flex items-center justify-center">发布作业</a-button>
      </div>
    </div>

    <div class="contentItem">
      <div class="flex items-center justify-between" style="border-bottom: 1px solid #E8E8E8;height: 54px;">
        <div class="num">题量0道，总分0分</div>
        <div class="flex items-center justify-between">
           <a-button type="text" class="choosebut flex items-center justify-center" @click="question">题库选题</a-button>
          <a-button type="text" class="choosebut flex items-center justify-center" style="margin-left: 20px;" @click="aiquestion">AI生成</a-button>
        </div>
      </div>


      <div v-if="true">
        <Item :type="'view'"></Item>
      </div>
      <div class="flex justify-center" style="margin-top: 92px;" v-else>
        <img src="@/assets/image/zanwu/questionNodata.png" alt="" style="width: 309px;height: 231px;" />
      </div>
    </div>

  </div>
</template>

<script lang="ts" setup>
import { useCourseStore } from '@/stores/course'
const courseStore = useCourseStore()
const { homeworkDetails } = toRefs(courseStore)

import Item from '@/components/question/Item.vue'
import { twMerge } from 'tailwind-merge'
import back from '@/components/ui/back.vue';
import { ref, reactive } from 'vue';
import { useRouter,useRoute  } from 'vue-router'
const router = useRouter() //路由跳转
const orderValue = ref(1)


onMounted(() => {
  console.log(homeworkDetails.value,'homewokeDetails.value')
})


function question() {
  router.push('/question/list')
}
function aiquestion() {
  router.push('/question/ailist')
}


</script>
<style lang="scss" scoped>
.mian {
  min-height: 100vh;
  background: #F0F9FF;
  display: flex;
  flex-direction: column;
  padding: 0 40px;
  background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center bottom;
  min-width: 907px;
  width: 100%;
  overflow: auto;
}

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title {
    font-size: 24px;
    font-weight: 700;
    line-height: 12px;
    margin: 20px 0;
  }
}

.content {
  height: 99px;
  opacity: 1;
  border-radius: 0.65px;
  background: #FFFFFF;
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  padding: 20px;

  .name {
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 16px;
  }

  .fabu {
    width: 82px;
    height: 32px;
    opacity: 1;
    border-radius: 150px;
    background: linear-gradient(90deg, #3F8CFF 0%, #15C0E6 100%);
    font-size: 14px;
    color: #fff
  }

  .setting {
    /** 文本1 */
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(51, 51, 51, 1);

    span {
      width: 78.77px;
    }
  }
}

.contentItem {
  // height: 596px;
  opacity: 1;
  border-radius: 0.65px;
  background: #FFFFFF;
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  padding: 0 20px 20px 20px;
  margin-top: 20px;

  .num {
    font-size: 14px;
    font-weight: 400;
    line-height: 14px;
  }

  .choosebut {
    background-color: #3F8CFF;
    border-radius: 3px;
    font-size: 14px;
    font-weight: 500;
    line-height: 14px;
    color: #fff;
    height: 24px;
    // width: 76px;
    text-align: center;
  }
}
</style>