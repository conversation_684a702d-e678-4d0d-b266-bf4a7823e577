<script lang="ts">
  type FormState = {
    chunkMethod: string
    delimiter?: string
    keywordNum?: number
    questionNum?: number
  }

  const chunkMethodsByFileExt: Record<string, string[]> = {
    pdf: ['naive', 'resume', 'manual', 'paper', 'book', 'laws', 'presentation', 'one'],
    docx: ['naive', 'resume', 'book', 'laws', 'one'],
    doc: ['naive', 'resume', 'book', 'laws', 'one'],

    xlsx: ['naive', 'one', 'qa', 'table'],
    xls: ['naive', 'one', 'qa', 'table'],

    ppt: ['naive', 'presentation'],
    pptx: ['naive', 'presentation'],

    txt: ['naive', 'resume', 'book', 'laws', 'one', 'qa', 'table'],
    csv: ['naive', 'qa', 'table'],
    md: ['naive'],
    json: ['naive'],
  }

  function shouldShowDelimiter(chunkMethod: string) {
    return ['naive', 'book', 'laws', 'one'].includes(chunkMethod)
  }

  function shouldShowKeywordsAndQuestions(chunkMethod: string) {
    return ['naive', 'manual', 'paper', 'book', 'laws', 'presentation', 'one', 'picture'].includes(
      chunkMethod,
    )
  }
</script>

<script setup lang="ts">
  import { message, type FormProps } from 'ant-design-vue'
  import type { MutationStatus } from '@tanstack/vue-query'

  import { injectKbDetailContext } from '@/utils/context/kb'

  const props = withDefaults(defineProps<FormProps & { rawFile: API.Kb.File }>(), {
    layout: 'vertical',
  })

  const { rawKb } = injectKbDetailContext()

  const fileExt = computed(() => getFilenameParts(props.rawFile.name).ext)
  const supportedChunkMethods = computed(
    () => chunkMethodsByFileExt[fileExt.value] ?? chunkMethodsByFileExt['txt'],
  )

  const formState = ref<FormState>({
    chunkMethod: props.rawFile.chunk_method,
    delimiter: props.rawFile.parser_config?.delimiter ?? '',
    keywordNum: props.rawFile.parser_config?.auto_keywords ?? 0,
    questionNum: props.rawFile.parser_config?.auto_questions ?? 0,
  })

  const { mutate: updateFile, status } = useMutation({
    mutationFn() {
      return kbFetcher(`/datasets/${rawKb.value!.id}/documents/${props.rawFile.id}/`, {
        method: 'put',
        body: {
          chunk_method: formState.value.chunkMethod,
          parser_config: {
            auto_keywords: formState.value.keywordNum,
            auto_questions: formState.value.questionNum,
            delimiter: formState.value.delimiter,
          },
        } as Partial<API.Kb.File>,
      })
    },
    onError(err) {
      message.error(err.message)
    },
    onSuccess() {
      message.success('修改配置成功')
    },
  })

  const emit = defineEmits<{ 'update:status': [MutationStatus] }>()
  watchEffect(() => emit('update:status', status.value))
</script>

<template>
  <AForm
    v-bind="props"
    :model="formState"
    @finish="updateFile()"
  >
    <AFormItem
      required
      label="分块方法"
      name="chunkMethod"
    >
      <ASelect v-model:value="formState.chunkMethod">
        <ASelectOption
          v-for="method in supportedChunkMethods"
          :key="method"
          :value="method"
        >
          {{ method }}
        </ASelectOption>
      </ASelect>
    </AFormItem>

    <AFormItem
      v-if="shouldShowDelimiter(formState.chunkMethod)"
      label="分隔符"
      required
      name="delimiter"
    >
      <AInput v-model:value="formState.delimiter" />
    </AFormItem>

    <AFormItem
      v-if="shouldShowKeywordsAndQuestions(formState.chunkMethod)"
      label="关键词数量"
      name="keywordNum"
      required
    >
      <div class="flex gap-4">
        <ASlider
          class="reset-ant-slider grow"
          v-model:value="formState.keywordNum"
        />
        <AFormItemRest>
          <AInputNumber
            class="max-w-15"
            v-model:value="formState.keywordNum"
          />
        </AFormItemRest>
      </div>
    </AFormItem>

    <AFormItem
      v-if="shouldShowKeywordsAndQuestions(formState.chunkMethod)"
      label="问题数量"
      name="questionNum"
      required
    >
      <div class="flex gap-4">
        <ASlider
          class="reset-ant-slider grow"
          v-model:value="formState.questionNum"
        />
        <AFormItemRest>
          <AInputNumber
            class="max-w-15"
            v-model:value="formState.questionNum"
          />
        </AFormItemRest>
      </div>
    </AFormItem>
  </AForm>
</template>
