<template>
    <div class="page">
      <el-main class="page-content">
        <div @click="handleBack" class="back-btn">
          <img style="width: 0.8vw;height: 0.8vw;" src="@/assets/image/ArrowLeft.svg"/>
          <span class="back-text">返回</span>
        </div>
        <h1 class="text text-xl">{{ courseClassStore.title }}-班级管理</h1>
        <div class="class-manager">
          <ClassManager />
        </div>
        
      </el-main>
    </div>
  </template>
  
  <script lang="ts" setup>
  //引入接口
  import { ref, computed, markRaw, onMounted} from 'vue'
  import { ElMessageBox } from 'element-plus'
  import { WarningFilled, } from '@element-plus/icons-vue'
  import { useRouter } from 'vue-router'
  import ClassManager from '@/components/courseClass/classManager.vue'
  import { useCourseClassStore } from '@/stores/courseClass'

const courseClassStore = useCourseClassStore()
  
  const router = useRouter()
  
  // 模拟数据
//   const files = ref([])
//   const search = ref('')
//   const total = ref(0)
//   const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getPptList);
  
  const handleBack = () => {
    router.back()
  }
  
  const delMessage = () => {
      ElMessageBox.confirm(
      '确定删除本记录吗？删除后无法恢复！',
      {
        type: 'warning',
        icon: markRaw(WarningFilled),
      }
    )
  }

  const checkedKeys = ref<Array<number | string>>([])

  </script>
  
  <style scoped>
  .page {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
  }

  .back-btn {
    cursor: pointer;
    margin-top: 2vw;
    font-size: 1vw;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5vw;
  }
  .back-btn:hover {
    color: #3f8cff;
  }
  
  .page-content {
    width: 100%;
    height: 100vh;
    overflow: hidden;   
    background: url(@/assets/image/bg1.png) no-repeat center top;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 0 2vw;
  }
  
  .text {
    margin: 1vh 0;
    color: #333;
  }
  .class-manager {
    background-color: #ffffff;
    border-radius: 5px;
    padding: 8px;
    max-height: 87vh;
    overflow: auto;
  }

  </style>
  