<template>
    <div class="containerq">
        <div class="flex top">
            <div class="back-btn" @click="() => router.push('/homework/handquetion')">
                <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
                <span style="font-size: 14px;line-height: 14px;">返回</span>
            </div>
            <div class="title flex items-center justify-center">我的题库</div>
        </div>

        <div class="quetion">
            <div class="num">共6道试题</div>
            <div class="search">
                <div class="flex items-center justify-between flex-wrap gap-5">
                    <div class="flex items-center gap-5">
                        <a-select v-model:value="value1" style="width: 170px;" size="small" :options="options1"
                            placeholder="选择课程"></a-select>
                        <a-select v-model:value="value1" style="width: 170px;" size="small" :options="options1"
                            placeholder="归属人"></a-select>
                        <a-date-picker show-time placeholder="创建时间" v-model:value="params.created_at"
                            style="width: 237px;" size="small" format="YYYY-MM-DD HH:mm:ss"
                            value-format="YYYY-MM-DD HH:mm:ss" @change="getquestionList" />
                    </div>
                    <a-input placeholder="输入关键字" v-model:value="params.stem" size="small" style="width: 258px;"
                        @pressEnter="getquestionList">
                        <template #suffix>
                            <SearchOutlined style="color: #C4C4C4;" @click="getquestionList" />
                        </template>
                    </a-input>
                </div>
                <div class="flex  items-center " style="margin-top: 17px;">
                    <div style="width: 42px;">题型：</div>
                    <div>
                        <a-radio-group v-model:value="params.type" @change="changeRadio">
                            <a-radio :value="''">全选</a-radio>
                            <a-radio :value="'单选题'">单选题</a-radio>
                            <a-radio :value="'多选题'">多选题</a-radio>
                            <a-radio :value="'填空题'">填空题</a-radio>
                            <a-radio :value="'判断题'">判断题</a-radio>
                            <a-radio :value="'问答题'">问答题</a-radio>
                        </a-radio-group>
                    </div>
                </div>
            </div>
            <!-- 全选 -->
            <div v-if="true">
                <div class="flex items-center mt-[15px]">
                    <a-checkbox v-model:checked="checkedAll" style="margin-left: 10px;">全选</a-checkbox>
                    <a-button type="text" size="small"
                        style="border: 1px solid #3F8CFF;color: #3F8CFF;margin-left: 20px;"
                        @click="awayQuetion">收起题目详情</a-button>
                </div>
                <Item :isOpen="isOpen" :questionLists="questionLists" :chooseType="'homework'"
                    @chooseQue="chooseQuetion"></Item>
            </div>
            <div v-else>
                <img src="@/assets/image/zanwu/nodata.png" style="width: 309px;height: 301px;margin:21vh auto 0" />
            </div>
        </div>


        <div class="footer flex items-center justify-end" style="padding-right: 260px;">
            <div class="pagination">
                <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total"
                    layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </div>
            <div class="numchoose">
                已选0道题目
            </div>
            <a-button type="text" class="footbut" @click="setJoinExam">加入作业</a-button>
            
        </div>
    </div>
</template>
<script lang="ts" setup name="List">
import { joinExam } from '@/services/api/exam'
import { usePagination } from '@/hooks/usePagination'; //解构分页参数
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getquestionList);

import Item from '@/components/question/Item.vue';
import { reactive, onMounted, ref, watch } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router'
const router = useRouter()

import { useCourseStore } from '@/stores/course'
const courseStore = useCourseStore()
const { homeworkDetails,user_id } = toRefs(courseStore)

import { parseMarkdown } from '@/utils/markdownParser'; // 引入工具函数
import { questionList, deleteQuestions, deleteQuestionsBatch, excelAddQue } from '@/services/api/question'
import { openMessage, openMessageBox, getEn, levelTransform, questionTypeData } from "@/utils/util";
import type { ComponentSize } from 'element-plus'

import type { Dayjs } from 'dayjs';


import type { SelectProps } from 'ant-design-vue';
const value1 = ref();
const options1 = ref<SelectProps['options']>([
    {
        value: '1',
        label: '课程1',
    },
    {
        value: '2',
        label: '课程2',
    },
    {
        value: '3',
        label: '课程3',
    },
]);
const onChange = (value: Dayjs, dateString: string) => {
    console.log('Selected Time: ', value);
    console.log('Formatted Selected Time: ', dateString);
};

const onOk = (value: Dayjs) => {
    console.log('onOk: ', value);
};

const checkedAll = ref(false) //全选

//控制选项是否展开isOpen
const isOpen = ref(false)
function awayQuetion() {
    isOpen.value = !isOpen.value
}

//题目类型筛选
function changeRadio(e: any) {
    console.log('changeRadio', e)
    getquestionList()
}
const params = reactive({
    stem: '',
    type: '',
    created_at: '',
})

interface QuestionItem {
    id: number;
    type: string;
    stem: string;
    updated_at: string;
    author: string;
    difficulty: number;
    answer: string | string[];
    options: Array<{
        key: string;
        value: string;
    }>;
    explanation: string;
    checked: boolean;
}
const questionLists = ref([]) //题目列表

async function getquestionList() {
    console.log(params, '搜索参数')
    const param = {
        page: currentPage.value,
        page_size: pageSize.value,
        ...params
    }
    questionList(param).then((res: any) => {
        if (res.code == 200) {
            total.value = res.data.count
            questionLists.value = res.data.results.map((item: any) => {
                return {
                    ...item,
                    checked: chosenIdsSet.value.has(item.id) ? true : false,
                }
            })
            const nowquesIds = questionLists.value.map((item: any) => item.id);
            const isSubset = nowquesIds.every(id => chosenIdsSet.value.has(id)); // 判断当前题目ID数组是否是选中的题目ID数组的子集
            if (isSubset) {
                checkedAll.value = true
            } else {
                checkedAll.value = false
            }
        }
    }).catch(error => {
        console.error('获取失败:', error)
    })
}

function setJoinExam() {
    const param = {
        "name": homeworkDetails.value.name,
        "author": user_id.value,
        "direction": "记忆",
        "difficulty": "简单",
        "questions": Array.from(chosenIdsSet.value),
        "exam_id": homeworkDetails.value.id
    }
    console.log(param, '加入作业参数')
    // return
    joinExam(param).then((res: any) => {
        
    })
}

const chosenIdsSet = ref<Set<number | string>>(new Set());// 选中的题目ID数组
function chooseQuetion(item: QuestionItem) {
    if (chosenIdsSet.value.has(item.id)) {
        chosenIdsSet.value.delete(item.id)
    } else {
        chosenIdsSet.value.add(item.id)
    }
    console.log(chosenIdsSet.value, '选中的题目ID数组')
}
watch(checkedAll, (newValue) => {
    const nowquesIds = questionLists.value.map((item: any) => item.id); // 将所有题目的id添加到数组中
    if (newValue) {
        questionLists.value.forEach((item: any) => {item.checked = true;});
        nowquesIds.forEach(id => chosenIdsSet.value.add(id));
    } else {
        questionLists.value.forEach((item: any) => {item.checked = false; });
        nowquesIds.forEach(id => chosenIdsSet.value.delete(id));
    }
    
    console.log(chosenIdsSet.value, '选中的题目ID数组')
})






getquestionList()
</script>
<style lang="scss" scoped>
.back-btn {
    cursor: pointer;
    width: 60px;
    color: #333;
    display: flex;
    align-items: center;
    margin-left: 20px;
}

.title {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 12px;
    width: calc(100% - 60px - 20px);
}

.containerq {
    background: rgba(240, 249, 255, 1);
    min-height: 100vh;
    width: 100%;
    // min-width: 1400px;
    overflow: auto;
}

.top {
    height: 60px;
    opacity: 1;
    background: #FFFFFF;
    box-shadow: 0px 2px 10px rgba(67, 143, 254, 0.1);
    position: fixed;
    width: 100%;
    z-index: 9999;
}

.quetion {
    /* margin-top: 20px; */
    // min-width: 880px;
    /* width: 100%; */
    margin: 20px 260px 0;
    border-radius: 8.53px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
    min-height: calc(100vh - 100px);
    padding: 20px 20px 150px 20px;
}

.num {
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 14px;
    color: rgba(102, 102, 102, 1);
}

.numchoose {
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 0px;
    color: rgba(102, 102, 102, 1);
}

.search {
    opacity: 1;
    border-radius: 5px;
    background: rgba(63, 140, 255, 0.03);
    margin-top: 20px;
    padding: 20px 20px 20px 20px;
}



.footer {
    height: 80px;
    opacity: 1;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px -7px 10px rgba(67, 143, 254, 0.2);
    position: fixed;
    bottom: 0;
    width: 100%;
}


.footbut {
    width: 82px;
    height: 32px;
    opacity: 1;
    border-radius: 150px;
    background: linear-gradient(135.84deg, #219FFF 0%, #0066FF 100%);
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0px;
    line-height: 14px;
    color: #fff;
    margin-left: 42px;
}
:deep(.el-pager li) {
  min-width: 22px !important;
  height: 22px !important;
}

:deep(.el-pager li.is-active) {
  font-size: 14px;
  color: #ffffff;
  background-color: #3f8cff;
}
:deep(.btn-prev) {
  background-color: transparent !important; /* 完全透明 */
}
:deep(.btn-next) {
  background-color: transparent !important; /* 完全透明 */
}
</style>