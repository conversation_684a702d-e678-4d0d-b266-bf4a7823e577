<script lang="ts">
  import type { ColumnType } from 'ant-design-vue/es/table'

  export interface TableItem {
    id: string
    name: string
    source: string
    replicaNum: number
    activeReplicaNum: number
    createTime: Date
    rawIndex: number
  }

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 240,
    },
    {
      title: '路径',
      dataIndex: 'source',
      key: 'source',
      width: 320,
    },
    {
      title: '副本数',
      key: 'replicas',
      customRender: ({ record }) => `${record.activeReplicaNum} / ${record.replicaNum}`,
      width: 160,
    },
    {
      title: '创建时间',
      key: 'createTime',
      customRender: ({ record }) => record.createTime.toLocaleString('zh-CN'),
      width: 200,
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
    },
  ] as const satisfies ColumnType<TableItem>[]
</script>

<script setup lang="ts">
  import { twMerge } from 'tailwind-merge'

  import { injectModelsContext } from '@/utils/context/models'

  const props = defineProps<{
    models: TableItem[]
  }>()

  const { selectedModels, setSelectedModels } = injectModelsContext()
</script>

<template>
  <ATable
    :class="
      twMerge(
        'reset-ant-table-pagination',
        '**:[.ant-table]:bg-transparent! **:[td,th]:border-[#CEDFF2FF]!',
        '**:[th]:rounded-none! **:[th]:border-t! **:[th]:bg-transparent! **:[th]:before:hidden',
        '**:[.ant-table-cell.ant-table-cell-row-hover]:bg-foreground-1/3! **:[.ant-table-cell]:bg-transparent!',
        '**:[.ant-table-row-expand-icon-cell]:max-w-12!',
      )
    "
    :columns="columns"
    :data-source="props.models"
    :pagination="{ size: 'small' }"
    :scroll="{ x: 1200 }"
    :row-key="(record: TableItem) => record.id"
    :row-selection="{
      selectedRowKeys: selectedModels,
      onChange(selectedRowKeys) {
        setSelectedModels(selectedRowKeys as string[])
      },
    }"
    :row-expandable="(record: TableItem) => record.replicaNum > 0"
  >
    <template
      #bodyCell="// @ts-expect-error antdv poor typing
      { column, record, }: { record: TableItem; column: (typeof columns)[number] }"
    >
      <div
        v-if="column.key === 'action'"
        class="flex items-center space-x-2"
      >
        <ModelActionEdit
          :raw-index="record.rawIndex"
          class="-ml-1!"
        />
        <ModelActionDelete :raw-index="record.rawIndex" />
        <ModelActionStop
          v-if="record.replicaNum > 0"
          :raw-index="record.rawIndex"
        />
        <ModelActionRun
          v-else
          :raw-index="record.rawIndex"
        />
      </div>
    </template>

    <template #expandedRowRender="{ record }">
      <ModelInstanceTable :model-id="record.id" />
    </template>
  </ATable>
</template>
