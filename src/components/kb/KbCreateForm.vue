<script lang="ts">
  export interface FormValues {
    name: string
  }
</script>

<script setup lang="ts">
  import type { FormProps } from 'ant-design-vue'
  import type { MutationStatus } from '@tanstack/vue-query'

  const props = defineProps<FormProps>()

  const formValues = ref<Partial<FormValues>>({})

  const queryCLient = useQueryClient()
  const { mutate, status } = useMutation({
    mutationFn() {
      return kbFetcher('/datasets/', {
        method: 'post',
        body: formValues.value,
      })
    },
    onSuccess() {
      return queryCLient.invalidateQueries({ queryKey: kbQueryKey.kbList() })
    },
  })

  const emit = defineEmits<{ 'update:status': [MutationStatus] }>()
  watchEffect(() => emit('update:status', status.value))
</script>

<template>
  <AForm
    v-bind="props"
    :model="formValues"
    @finish="mutate()"
  >
    <AFormItem
      label="名称"
      name="name"
      required
    >
      <AInput
        v-model:value="formValues.name"
        placeholder="请输入知识库名称"
      />
    </AFormItem>
  </AForm>
</template>
