<script setup lang="ts">
  import { injectKbDetailContext } from '@/utils/context/kb'

  const { rawKb } = injectKbDetailContext()

  const chunkMethod = ref<string>()
  const delimiter = ref<string>()
  const keywordNum = ref<number>()
  const questionNum = ref<number>()

  watch(
    () => rawKb.value?.chunk_method,
    (value) => {
      chunkMethod.value = value
    },
    { immediate: true },
  )
  watch(
    () => rawKb.value?.parser_config?.delimiter,
    (value) => {
      delimiter.value = value
    },
    { immediate: true },
  )
  watch(
    () => rawKb.value?.parser_config?.auto_keywords,
    (value) => {
      keywordNum.value = value ?? 0
    },
    { immediate: true },
  )
  watch(
    () => rawKb.value?.parser_config?.auto_questions,
    (value) => {
      questionNum.value = value ?? 0
    },
    { immediate: true },
  )
</script>

<template>
  <div class="space-y-5!">
    <div class="text-foreground-2 text-lg font-bold">分块参数设置</div>

    <AFormItem
      required
      label="分块方法"
    >
      <ASelect
        v-model:value="chunkMethod"
        name="chunkMethod"
      >
        <ASelectOption value="1">方法1</ASelectOption>
        <ASelectOption value="2">方法2</ASelectOption>
      </ASelect>
    </AFormItem>

    <AFormItem
      label="分隔符"
      required
    >
      <AInput
        v-model:value="delimiter"
        name="delimiter"
      />
    </AFormItem>

    <AFormItem
      label="关键词数量"
      required
    >
      <div class="flex gap-4">
        <ASlider
          class="reset-ant-slider grow"
          name="keywordNum"
          v-model:value="keywordNum"
          :tooltip-open="false"
        />
        <AFormItemRest>
          <AInputNumber
            class="max-w-15"
            v-model:value="keywordNum"
          />
        </AFormItemRest>
      </div>
    </AFormItem>

    <AFormItem
      label="问题数量"
      required
    >
      <div class="flex gap-4">
        <ASlider
          class="reset-ant-slider grow"
          name="questionNum"
          v-model:value="questionNum"
          :tooltip-open="false"
        />
        <AFormItemRest>
          <AInputNumber
            class="max-w-15"
            v-model:value="questionNum"
          />
        </AFormItemRest>
      </div>
    </AFormItem>
  </div>
</template>
