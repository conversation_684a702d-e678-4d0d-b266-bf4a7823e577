import http from "@/services/http";

//登录
export const courseLogin = (params: any) => {
    return http({
        url: `api/v1/cas/login/`,
        method: "post",
        data: params
    })
}

//添加课程
export const courseAdd = (params: any) => {
    return http({
        url: `api/v1/course/`,
        headers: {
            "Content-Type": "multipart/form-data",
        },
        method: "post",
        data: params
    })
}
//修改课程
export const courseEdit = (params: any) => {
    return http({
        url: `api/v1/course/${params.get('id')}/`,
        headers: {
            "Content-Type": "multipart/form-data",
        },
        method: "put",
        data: params
    })
}
//获取课程列表
export const courseListapi = (params: any) => {
    return http({
        url: `api/v1/course/`,
        method: "get",
        params: params
    })
}
//删除课程
export const courseDelete = (id: any) => {
    return http({
        url: `api/v1/course/${id}/`,
        method: "delete",
    })
}

//学期列表 api/v1/semester/
export const semesterList = () => {
    return http({
        url: `api/v1/semester/`,
        method: "get",
        // params: params
    })
}
//新增章节api/v1/chapter/
export const chapterAdd = (params: any) => {
    return http({
        url: `api/v1/chapter/`,
        method: "post",
        data: params
    })
}
//章节列表api/v1/chapter/?course_id=28
export const chapterList = (params: any) => {
    return http({
        url: `api/v1/chapter/`,
        method: "get",
        params: params
    })
}
// 课程章节指定目录 api/v1/chapter/tables/?chapter_id=2
export const chapterTables = (params: any) => {
    return http({
        url: `api/v1/chapter/tables/?chapter_id=${params}`,
        method: "get",
    })
}


//章节列表详情 api/v1/chapter/19/
export const chapterDetail = (params: any) => {
    return http({
        url: `api/v1/chapter/${params}/`,
        method: "get",
    })
}
//章节修改
export const chapterEdit = (params: any) => {
    return http({
        url: `api/v1/chapter/${params.id}/`,
        method: "put",
        data: params
    })
}
//章节删除api/v1/chapter/24/、
export const chapterDelete = (params: any) => {
    return http({
        url: `api/v1/chapter/${params}/`,
        method: "delete",
    })
}

//知识点添加api/v1/knowledge_point/
export const knowledgeAdd = (params: any) => {
    return http({
        url: `api/v1/knowledge_point/`,
        method: "post",
        data: params
    })
}
//知识点修改api/v1/knowledge_point/1/
export const knowledgeEdit = (params: any) => {
    return http({
        url: `api/v1/knowledge_point/${params.id}/`,
        method: "put",
        data: params
    })
}

//知识点删除 api/v1/knowledge_point/1/
export const knowledgeDelete = (params: any) => {
    return http({
        url: `api/v1/knowledge_point/${params}/`,
        method: "delete",
    })
}


//教案取消绑定 api/v1/teachplan/batch_unbind/
export const teachplanUnbind = (params: any) => {
    return http({
        url: `api/v1/teachplan/batch_unbind/`,
        method: "post",
        data: params
    })
}
//PPT取消绑定api/v1/ppt/batch_unlink/
export const pptUnbind = (params: any) => {
    return http({
        url: `api/v1/ppt/batch_unlink/`,
        method: "post",
        data: params
    })
}
