<script setup lang="ts">
  import { injectKbFilesContext, injectKbDetailContext } from '@/utils/context/kb'
  import type { MutationStatus } from '@tanstack/vue-query'

  import IconSetting from '~icons/lucide/settings'

  const props = defineProps<{ rawIndex: number }>()

  const { rawFiles } = injectKbFilesContext()
  const { rawKb } = injectKbDetailContext()

  const rawFile = computed(() => rawFiles.value[props.rawIndex])

  const isConfigModalOpen = ref(false)
  const configFormStatus = ref<MutationStatus>('idle')
</script>

<template>
  <AButton
    type="text"
    size="small"
    class="icon-a-button"
    title="设置"
    @click="isConfigModalOpen = true"
  >
    <IconSetting />

    <AModal
      v-model:open="isConfigModalOpen"
      title="配置"
      :ok-button-props="{
        htmlType: 'submit',
        // @ts-expect-error antdv poor typing
        form: 'file-config-form',
        loading: configFormStatus === 'pending',
      }"
      wrap-class-name="reset-ant-modal"
      centered
    >
      <KbFileConfigForm
        id="file-config-form"
        :raw-file="rawFile"
        @update:status="configFormStatus = $event"
      />
    </AModal>
  </AButton>
</template>
