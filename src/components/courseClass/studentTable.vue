<template>
  <div class="table-container">
  <el-table :data="students" :cell-style="{ textAlign:'center' }" :header-cell-style="{ 'text-align': 'center' }" style="max-width: 100%;" max-height="65vh" @selection-change="handleSelectionChange">
    <el-table-column type="selection" width="50" />
    <el-table-column prop="name" label="姓名" width="80" show-overflow-tooltip />
    <el-table-column prop="studentId" label="学号" show-overflow-tooltip />
    <el-table-column prop="department" label="院系" show-overflow-tooltip />
    <el-table-column prop="major" label="专业" show-overflow-tooltip />
    <el-table-column label="操作" width="80">
      <template #default="scope">
        <el-button type="text" :icon="Delete" @click="$emit('delete-student', scope.row.id)"></el-button>
      </template>
    </el-table-column>
  </el-table>
  </div>
  <el-pagination
    @current-change="$emit('page-change', $event)"
    :current-page="currentPage"
    :page-size="10"
    :total="total"
    size="small"
    layout="prev, pager, next"
  />
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, ref } from 'vue'
import { Delete } from '@element-plus/icons-vue'
const props = defineProps<{ students: any[], selectedClass: any, currentPage: number, total: number }>()
const emits = defineEmits(['delete-student', 'page-change', 'selection-change'])

const handleSelectionChange = (selection: any[]) => {
  emits('selection-change', selection)
}

</script>

<style scoped>
.table-container {
  overflow: auto;
}
</style>