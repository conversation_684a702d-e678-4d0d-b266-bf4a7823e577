<script setup lang="ts">
  import { injectModelsContext } from '@/utils/context/models'

  import IconEdit from '~icons/local/pencil-square'

  type Props = {
    rawIndex: number
  }
  const props = defineProps<Props>()

  const {
    setDeployFormRawModel,
    setIsDeployModalOpen,
    setFormApi: setFormAction,
    rawModels,
  } = injectModelsContext()

  function handleEdit() {
    setDeployFormRawModel(rawModels.value[props.rawIndex])
    setFormAction('update')
    setIsDeployModalOpen(true)
  }
</script>

<template>
  <AButton
    type="text"
    size="small"
    class="icon-a-button"
    title="编辑"
    @click="handleEdit"
  >
    <IconEdit />
  </AButton>
</template>
