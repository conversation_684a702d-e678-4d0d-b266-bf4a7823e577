<template>
  <div>
    <div class="list-top">
        <span>班级列表</span>
        <el-button type="primary" size="small" @click="$emit('add-class')">新建</el-button>
    </div>
    <div class="list-container">
      <el-menu class="custom-class-menu" :default-active="selectedClass?.id" @select="onSelect">
        <el-menu-item
          v-for="cls in classes"
          :key="cls.id"
          :index="cls.id"
          class="flex-item-container has-dot"
        >
          <span class="class-name">{{ cls.name }}</span>
          <el-dropdown class="actions-dropdown">
            <el-button :icon="MoreFilled" size="mini" circle class="icon-btn"/>
            <template #dropdown>
              <el-dropdown-menu>
                <!-- <el-dropdown-item :icon="Edit" @click="$emit('rename-class', cls.id)">重命名</el-dropdown-item> -->
                <el-dropdown-item :icon="Operation" @click="$emit('settings-class', cls.id)">班级设置</el-dropdown-item>
                <el-dropdown-item :icon="CircleClose" @click="$emit('delete-class', cls.id)">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-menu-item>
      </el-menu>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { defineProps, defineEmits, ref, reactive } from 'vue'
  import { MoreFilled, Edit, Operation, CircleClose } from '@element-plus/icons-vue'
const props = defineProps<{ classes: any[], selectedClass: any }>()
const emits = defineEmits(['add-class', 'rename-class', 'settings-class', 'delete-class', 'update:selectedClass'])

function onSelect(id: number) {
  const cls = props.classes.find(c => c.id === id)
  emits('update:selectedClass', cls)
}

</script>

<style scoped>
.list-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.list-container {
  overflow: auto;
  max-height: 78vh;
  padding: 5px 0 5px 0;
}
.custom-class-menu {
  border-right: none;
}
/* 菜单项容器 */
.flex-item-container {
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px !important;
}
.has-dot::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #3f8cff;
  margin-right: 8px;
  vertical-align: middle;
}
/* 左侧数据区域 */
.class-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;/* 防止长名称溢出 */
  white-space: nowrap;
}
/* 右侧按钮区域 */
.actions-dropdown {
  margin-left: 10px;
}
/* 自定义按钮样式 */
.icon-btn {
  width: 24px !important;
  height: 24px;
  padding: 0;
}
.icon-btn .el-icon {
  font-size: 14px;
}
</style>