<script setup lang="ts">
import singleSelect from '@/components/question/singleSelect.vue'; // 引入单选题组件
import fillEmpty from '@/components/question/fillEmpty.vue'; // 引入填空题组件
import multipleSelect from '@/components/question/multipleSelect.vue'; // 引入多选题组件
import judgeSelect from '@/components/question/judgeSelect.vue';
import essay from '@/components/question/essay.vue';
import { ref, reactive, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { selectQuestion } from '@/services/api/question'

const openModal = ref(false)

const radio = ref('1')
const router = useRoute()
const questionId = ref(router.query.qid)
const questionDatails = ref({})
const isType = ref('add')//判断是查看还是编辑 add edit

const typeQuestion = ref()

onMounted( async () => {
    //判断是什么题型
    if (typeQuestion.value == '单选') {
        radio.value = '1'
    } else if (typeQuestion.value == '多选') {
        radio.value = '2'
    } else if (typeQuestion.value == '判断') {
        radio.value = '3'
    } else if (typeQuestion.value == '填空') {
        radio.value = '4'
    } else if (typeQuestion.value == '问答') {
        radio.value = '5'
    }
    
    //获取题目详情信息
    if (!questionId.value) {
        isType.value = 'add'
        return
    }else{
        isType.value = 'edit'
    }
    
    const res = await selectQuestion(questionId.value)
    questionDatails.value = res.data
})

const props = defineProps({
  open: Boolean
})

watch(() => props.open, (val) => {
    openModal.value = val
})
const emit = defineEmits(['update:open','update:success']); // 定义自定义事件
watch(openModal, (val) => {
  emit('update:open', val)
  emit('update:success')
})
</script>

<template>
    <div>
        <a-modal v-model:open="openModal" title="新增题目" closable :footer="null"  width="1214px">
            <div class="pl-[34px] pr-[40px]">
                <div class="pt-[40px] pb-[20px] flex">
                    <div class="shrink-[0] text-[#000] text-[14px]  w-[69px]  text-right relative before:content-['*'] before:size-[5px] before:absolute before:top-0.5 before:left-4 before:text-[#FF4D4F]">
                        题型：
                    </div>
                    <div>
                        <a-radio-group v-model:value="radio" name="radioGroup">
                            <a-radio value="1" style="color:  rgba(0, 0, 0, 0.65);">单选</a-radio>
                            <a-radio value="2" style="margin: 0 41px;color:  rgba(0, 0, 0, 0.65);">多选</a-radio>
                            <a-radio value="3" style="color:  rgba(0, 0, 0, 0.65);">判断</a-radio>
                            <a-radio value="4" style="margin: 0 41px;color:  rgba(0, 0, 0, 0.65);">填空</a-radio>
                            <a-radio value="5" style="color:  rgba(0, 0, 0, 0.65);">问答</a-radio>
                        </a-radio-group>
                    </div>
                    <!-- <el-radio-group v-model="radio">
                        <el-row>
                            <el-col :span="24" v-if="isType === 'add' || typeQuestion == '单选题'">
                                <el-radio :value="1">单选题</el-radio>
                            </el-col>
                            <el-col :span="24" v-if="isType === 'add' || typeQuestion == '多选题'">
                                <el-radio :value="2" style="height: 100px;">多选题</el-radio>
                            </el-col>
                            <el-col :span="24" v-if="isType === 'add' || typeQuestion == '判断题'">
                                <el-radio :value="3">判断题</el-radio>
                            </el-col>
                            <el-col :span="24" v-if="isType === 'add' || typeQuestion == '填空题'">
                                <el-radio :value="4" style="height: 100px;">填空题</el-radio>
                            </el-col>
                            <el-col :span="24" v-if="isType === 'add' || typeQuestion == '问答题'">
                                <el-radio :value="5">问答题</el-radio>
                            </el-col>
                        </el-row>
                    </el-radio-group> -->
                </div>

                <div class="select" v-if="isType === 'add'">
                    <singleSelect :questionDetails="questionDatails" :isType="isType" :questionType="'单选题'" v-if="radio == '1'" @closeModal="openModal = false" />
                    <multipleSelect :questionDetails="questionDatails" :isType="isType" :questionType="'多选题'"  v-if="radio == '2'" @closeModal="openModal = false" />
                    <judgeSelect :questionDetails="questionDatails" :isType="isType" :questionType="'判断题'" v-if="radio == '3'" @closeModal="openModal = false" />
                    <fillEmpty :questionDetails="questionDatails" :isType="isType" :questionType="'填空题'"  v-if="radio == '4'" @closeModal="openModal = false" />
                    <essay :questionDetails="questionDatails" :isType="isType" :questionType="'问答题'" v-if="radio == '5'" @closeModal="openModal = false" />
                </div>
            </div>
        </a-modal>
    </div>
</template>
<style lang="scss" scoped>


</style>