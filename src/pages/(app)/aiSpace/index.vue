<template>
    <div class="space">
        <div class="space-left" :class="{ 'collapsed': isCollapsed }">
            <div class="left1">
                <img src="@/assets/image/img/aiIconc.png" style="width: 24px;height: 24px;" />
                <img src="@/assets/image/img/AIspaceText.png" style="width: 61px;height: 20px;margin-left: 8px;" />
            </div>

            <div class="left_item" v-for="(item, index) in leftItems" :class="{ active: item.isActive }"
                @click="handleItemClick(index)">
                <div class="flex" style="font-size: 12px;">
                    <el-icon>
                        <!-- <CreditCard /> -->
                        <component :is="item.icon" />
                    </el-icon>
                    <div style="margin-left: 8px;width: 48px;">{{ item.label }}</div>
                </div>
                <el-icon :size="12" v-if="item.isActive">
                    <Right />
                </el-icon>
            </div>
        </div>
        <!-- 悬浮按钮 -->
        <div class="toggle-btn z-index-999" @click="toggleCollapse">
            <el-icon size="10">
                <ArrowRight v-if="isCollapsed" />
                <ArrowLeft v-else />
            </el-icon>
        </div>

        <div class="space-right">
            <div class="title">欢迎来AI空间</div>
            <div class="ftitle text-base">副标题文字副标题文字副标题文字副标题文字副标题文字副标题文字副标题文字副标题文字</div>
            <div class="flex mt-[63px] justify-center items-center">
                <img @click="interTeachPlan()" class="h-[227px] w-[300px]" src="@/assets/image/img/aiteachplan.png" />
                <img @click="interPptSquare()" class="h-[227px] w-[300px] mx-[162px]" src="@/assets/image/img/aippt.png" />
                <img @click="interQuestion()" class="h-[227px] w-[300px]" src="@/assets/image/img/aiquetion.png" />
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router'

const router = useRouter()
function interTeachPlan() {
    router.push('/lessonPlan')
};

function interPptSquare() {
    router.push('/pptSquare')
};
const interQuestion = () => {
    router.push('/quetionList')
};

const isCollapsed = ref(false);
const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value;
};

const leftItems = reactive([
    { icon: 'CreditCard', label: '教学材料', isActive: true },
    { icon: 'Reading', label: '知识库', isActive: false },
    { icon: 'Reading', label: '知识图谱', isActive: false },
    { icon: 'Postcard', label: '摘要提取', isActive: false }
]);

const handleItemClick = (index: number) => {
    leftItems.forEach((item, i) => {
        item.isActive = i === index;
    });
    switch (index) {
        case 0:
            break;
        case 1:
            break;
        case 2:
            router.push('/graph');
            break;
        case 3:
            router.push('/abstract');
            break;
        default:
            break;
    }
};

</script>
<style scoped lang="less">
.space {
    background-image: url('@/assets/image/img/aispacebg.png');
    background-repeat: no-repeat;
    background-size: cover;
    min-height: 100vh;
    display: flex;
    overflow-x: auto;

    .space-left {
        position: relative;
        transition: width 0.3s ease; // 宽度变化动画
        overflow: hidden;
        width: 169px;
        // min-width: 169px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 2px 10px rgba(67, 143, 254, 0.1);
        border-left: 1px solid rgba(229, 229, 229, 1);
        flex-shrink: 0;

        &.collapsed {
            width: 0px; // 收起后的宽度
        }

        .left1 {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 28px 0 35px 0;
        }


        .left_item {
            width: 129px;
            height: 30px;
            opacity: 1;
            border-radius: 71px;
            margin: 0 auto 9px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 13px;
            cursor: pointer;

            &.active {
                background: rgba(63, 140, 255, 0.1);
                color: rgba(63, 140, 255, 1);
            }
        }

        .flex {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .toggle-btn {
        margin: auto;
        cursor: pointer;
        background-image: url('@/assets/image/img/ce.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        min-width: 24px;
        height: 67px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: black;
        font-weight: bold;
    }

    .space-right {
        display: flex;
        align-items: center;
        flex-direction: column;
        flex: 1;

        .title {
            font-weight: 600;
            margin-top: 270px;
            font-size: 30px;
            letter-spacing: 0px;
            line-height: 20px;
        }

        .ftitle {
            font-size: 16px;
            font-weight: 400;
            letter-spacing: 0px;
            line-height: 20px;
            margin-top: 19px
        }

        .item-container {
            width: 100%;
            display: flex;
            justify-content: center;
        }
    }

}
</style>