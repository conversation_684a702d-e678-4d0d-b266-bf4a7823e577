import type { AxiosResponse, AxiosError } from 'axios'
import type { FetchContext } from 'ofetch'
import { message } from 'ant-design-vue'

export const authAxiosInterceptor = {
  responseSuccess: (response: AxiosResponse) => {
    const authStore = useAuthStore()
    authStore.resetAuthAttempts()
    return response
  },
  responseError: async (error: AxiosError) => {
    const response = error.response

    if (response?.status !== 401) {
      return
    }

    const authStore = useAuthStore()

    if (authStore.isAuthAttemptsMaxed()) {
      message.error('登录失败，请重试')
      return
    }

    message.error('请登录')
    await new Promise((resolve) => setTimeout(resolve, 1500))
    authStore.attemptAuth()
  },
}

export const authOfetchInterceptor = {
  responseSuccess: () => {
    const authStore = useAuthStore()
    authStore.resetAuthAttempts()
  },
  responseError: async ({ response }: FetchContext) => {
    if (response?.status !== 401) {
      return
    }

    const authStore = useAuthStore()

    if (authStore.isAuthAttemptsMaxed()) {
      message.error('登录失败，请重试')
      return
    }

    message.error('请登录')
    await new Promise((resolve) => setTimeout(resolve, 1500))
    authStore.attemptAuth()
  },
}
