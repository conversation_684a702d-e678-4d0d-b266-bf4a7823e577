<script setup lang="ts">
  import { ComposerForm, injectChatContext } from '@wicii/chat-primitive'

  import type { ChatAttachment } from '@/types/chat'
  import { extraComposerContextInjectionKey } from '@/utils/context/chat'

  const { status } = injectChatContext()

  const attachments = ref<ChatAttachment[]>([])
  provide(extraComposerContextInjectionKey, { attachments })
</script>

<template>
  <ComposerForm class="composer-wrapper space-y-2 rounded-[10px] p-4">
    <ChatComposerAttachment
      type="kb"
      name="这是一个假的文件.pdf"
    />

    <ChatComposerTextarea />

    <div class="flex">
      <ChatComposerActionStop
        v-if="status === 'submitted' || status === 'streaming'"
        class="ml-auto"
      />
      <ChatComposerActionSend
        v-else
        class="ml-auto"
      />
    </div>
  </ComposerForm>
</template>

<style scoped>
  .composer-wrapper {
    --background-color: white;
    background: var(--background-color);
    border-width: 2px;
    border-color: var(--background-color);

    &:has(textarea:focus-visible) {
      border-color: transparent;
      background:
        linear-gradient(var(--background-color), var(--background-color)) padding-box,
        linear-gradient(145deg, rgba(73, 146, 255, 1), rgba(32, 195, 231, 1)) border-box;
    }
  }
</style>
