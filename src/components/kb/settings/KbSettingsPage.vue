<script setup lang="ts">
  import { injectKbDetailContext } from '@/utils/context/kb'

  const { rawKb } = injectKbDetailContext()
</script>

<template>
  <div class="px-(--page-px) py-5">
    <AForm
      id="kb-settings-form"
      class="rounded-md bg-white"
      layout="vertical"
    >
      <div class="border-separator border-b border-dashed shadow-[0_2px_19px_#1D4F99C] lg:flex">
        <div
          class="border-separator flex-3 border-b border-dashed px-10 py-7.5 lg:border-r lg:border-b-0 2xl:flex-2"
        >
          <KbBasicSettingsFormItems />
        </div>
        <div class="flex-2 px-10 py-7.5">
          <KbChunkSettingsFormItems />
        </div>
      </div>

      <div class="flex px-10 py-6">
        <AButton
          class="gradient-a-button ml-auto"
          type="primary"
          html-type="submit"
          form="kb-settings-form"
        >
          保存设置
        </AButton>
      </div>
    </AForm>
  </div>
</template>
