import { defineStore } from "pinia";
import { ref } from "vue";
// export interface questionState {
//     titleNumber: Number
// }

export const useQuestionStore = defineStore('question', {

    // 动作
    actions: {
        updateTitleNumber(value: number) {
            this.titleNumber = value;
        },
        prevTitle() {
            this.titleNumber--;
        },
        nextTitle() {
            this.titleNumber++
        }
    },
    // 状态
    state() {
        return {
            titleNumber: 1
        }
    },
    // 计算
    getters: {}
})