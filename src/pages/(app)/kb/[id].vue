<script setup lang="ts">
  import { TabsRoot, TabsContent } from 'reka-ui'

  import { kbDetailContextInjectionKey } from '@/utils/context/kb'

  const searchParams = useUrlSearchParams<Record<string, string>>('history')

  const route = useRoute('/(app)/kb/[id]')

  const { data: rawKb } = await useQuery({
    queryKey: kbQueryKey.kbDetail(() => route.params.id),
    async queryFn() {
      const { data } = await kbFetcher<{ data: { data: API.Kb.Kb[] } }>('/datasets/', {
        query: {
          id: route.params.id,
          source: 'external',
        },
      })
      return data.data[0]
    },
  }).suspense()

  provide(kbDetailContextInjectionKey, {
    rawKb: toRef(() => rawKb),
  })
</script>

<template>
  <TabsRoot
    class="h-full overflow-y-auto [--page-px:40px]"
    default-value="files"
    v-model="searchParams.tab"
    :unmount-on-hide="false"
  >
    <div class="flex items-center space-x-5 px-(--page-px) pt-22.5">
      <div class="text-foreground-2 text-xl font-bold">
        {{ rawKb?.name }}
      </div>

      <KbTabsList />
    </div>

    <TabsContent
      value="files"
      tabindex="-1"
    >
      <KbFilesPage />
    </TabsContent>

    <TabsContent
      value="settings"
      tabindex="-1"
    >
      <KbSettingsPage>
        <template #basic-settings>
          <KbBasicSettingsFormItems />
        </template>

        <template #chunk-settings>
          <KbChunkSettingsFormItems />
        </template>
      </KbSettingsPage>
    </TabsContent>
  </TabsRoot>
</template>
