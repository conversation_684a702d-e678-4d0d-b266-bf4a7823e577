<template>
    <el-dialog v-model="dialogShow"> 
        <vue-office-docx
            :src="props.item!.file"
            style="height: 100vh;"
        />

        <vue-office-pdf
            :src="props.item!.file"
            style="height: 100vh;"
        />

        <vue-office-pptx
            :src="props.item!.file"
            style="height: 100vh;"
        />
    </el-dialog>
</template>

<script lang="ts" setup>
import VueOfficeDocx from '@vue-office/docx/lib/v3' //引入VueOfficeDocx组件
import '@vue-office/docx/lib/v3/index.css' //引入相关样式
import VueOfficePdf from '@vue-office/pdf/lib/v3' //引入VueOfficePdf组件
import VueOfficePptx from '@vue-office/pptx/lib/v3' //引入VueOfficePptx组件

const props = defineProps({
  show: Boolean,
  item: Object
})

const dialogShow = ref(props.show);

watch(() => props.show, (newVal) => {
  dialogShow.value = newVal;
});

</script>

<style scoped lang="scss">
</style>