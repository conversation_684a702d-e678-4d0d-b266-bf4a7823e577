import type { ShallowRef } from 'vue'

type FileId = string

export const kbFilesContextInjectionKey = Symbol('kb-files') as InjectionKey<{
  selectedFiles: Readonly<ShallowRef<FileId[]>>
  setSelectedFiles(value: FileId[]): void

  rawFiles: Readonly<ShallowRef<API.Kb.File[]>>
}>

export function injectKbFilesContext() {
  const context = inject(kbFilesContextInjectionKey)
  if (!context) {
    throw new Error('kbContext is not provided')
  }
  return context
}

export const kbDetailContextInjectionKey = Symbol('kb-detail') as InjectionKey<{
  rawKb: Readonly<ShallowRef<API.Kb.Kb | undefined>>
}>

export function injectKbDetailContext() {
  const context = inject(kbDetailContextInjectionKey)
  if (!context) {
    throw new Error('kbDetailContext is not provided')
  }
  return context
}
