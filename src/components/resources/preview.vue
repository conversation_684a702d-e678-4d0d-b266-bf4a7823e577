<template>
    <a-modal
        v-model:open="visible"
        :title="props.item?.title" 
        :width=1000
        @cancel= "emitClose"
    > 
        <div class="preContent">

            <vue-office-docx
                v-if="props.item?.file_type === 'word'"
                :src=fileUrl
            />

            <vue-office-pdf
                v-if="props.item?.file_type === 'pdf'"
                :src=fileUrl
            />

            <vue-office-pptx
                v-if="props.item?.file_type === 'ppt'"
                :src=fileUrl
            />

            <videoPlay
                v-if="props.item?.file_type === 'video'"
                v-bind="options"
            />

            <a-image
                v-if="props.item?.file_type === 'image'"
                :src="fileUrl"
            />

            <!-- <div style="height: 60vh">
                <iframe :src="officePath" width="100%" height="100%" frameborder="0"></iframe>
            </div> -->

        </div>
    </a-modal>
</template>

<script lang="ts" setup>
import VueOfficeDocx from '@vue-office/docx/lib/v3' //引入VueOfficeDocx组件
import '@vue-office/docx/lib/v3/index.css' //引入相关样式
import VueOfficePdf from '@vue-office/pdf/lib/v3' //引入VueOfficePdf组件
import VueOfficePptx from '@vue-office/pptx/lib/v3' //引入VueOfficePptx组件
import videoPlay from "vue3-video-play/dist/index.mjs";//引入vue3-video-play组件
import "vue3-video-play/dist/style.css";//引入vue3-video-play组件样式
import { reactive } from "vue";

const props = defineProps({
  show: Boolean,
  item: Object
})

const visible = ref(false);
watch(() => props.show, (newVal) =>{
    console.log(newVal);
  visible.value = newVal
})

watch(() => props.show, (newVal) =>{
    console.log(newVal);
  visible.value = newVal
})

const emit = defineEmits(['update:show']);

const emitClose = () =>{
    emit('update:show', false)
}

// computed双向绑定 dialog 的 v-model
// const visible = computed({
//   get: () => props.show,
//   set: (val) => emit("updateShow", val)
// });

// computed获取传入的文件路径
const fileUrl = computed(() => props.item ? (props.item.file || props.item.contentUrl) : null);

const options = reactive({
  width: "100%", //播放器宽度
  height: "100%", //播放器高度
  color: "#1677ff", //主题色
  title: "", //视频名称
  src: fileUrl, //视频源
  muted: false, //静音
  webFullScreen: false,
  speedRate: ["0.75", "1.0", "1.25", "1.5", "2.0"], //播放倍速
  autoPlay: false, //自动播放
  loop: false, //循环播放
  mirror: false, //镜像画面
  ligthOff: false, //关灯模式
  volume: 0.3, //默认音量大小
  control: true, //是否显示控制
  controlBtns: [
    "audioTrack",
    "quality",
    "speedRate",
    "volume",
    "setting",
    "pip",
    "pageFullScreen",
    "fullScreen",
  ], //显示所有按钮,
});


const officePath = ref('')
const getOfficeUrl = (url: string) => {
  // URL必须是公共可访问
  return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(url)}`
}

// 图片预览列表
const srcList:any = []

// 监听 fileUrl 的变化
watch(fileUrl, (newVal, oldVal) => {
    srcList[0] = newVal
    officePath.value = getOfficeUrl(newVal)
});


</script>

<style scoped lang="scss">

:deep(.ant-image-preview-wrap){
    z-index: 1000;
}
.preContent{
    min-height: 400px;
    max-height: 800px;
    overflow: auto;
}
</style>