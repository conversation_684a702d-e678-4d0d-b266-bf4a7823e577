<script setup lang="ts">
  import { createMachine, assign } from 'xstate'
  import { useMachine } from '@xstate/vue'
  import { message } from 'ant-design-vue'

  import { injectKbFilesContext, injectKbDetailContext } from '@/utils/context/kb'

  import IconEdit from '~icons/local/pencil-square'
  import IconLoading from '~icons/lucide/loader-circle'

  type Props = {
    rawIndex: number
  }

  const props = defineProps<Props>()

  const { rawFiles } = injectKbFilesContext()
  const { rawKb } = injectKbDetailContext()

  const rawFile = computed(() => rawFiles.value[props.rawIndex])

  const fileNameParts = computed(() => getFilenameParts(rawFile.value.name))

  const queryClient = useQueryClient()
  const { mutate: renameFile, status: renameStatus } = useMutation({
    mutationFn() {
      return kbFetcher(`/datasets/${rawKb.value!.id}/documents/${rawFile.value.id}/`, {
        method: 'put',
        body: {
          name: snapshot.value.context.value + fileNameParts.value.ext,
        },
      })
    },
    onSuccess() {
      return queryClient.invalidateQueries({ queryKey: kbQueryKey.kbFiles(rawKb.value!.id) })
    },
    onError(err) {
      message.error(err.message)
    },
  })

  const fileNameMachine = createMachine({
    id: 'kb-file-name',
    context: {
      value: fileNameParts.value.name,
    },
    initial: 'reading',
    states: {
      reading: {
        on: { edit: 'editing' },
      },
      editing: {
        entry: assign({ value: () => fileNameParts.value.name }),
        on: {
          change: {
            actions: assign({ value: ({ event }) => event.value }),
          },
          commit: {
            actions: () => renameFile(),
            target: 'reading',
          },
          cancel: {
            target: 'reading',
          },
        },
      },
    },
  })

  const { send, snapshot } = useMachine(fileNameMachine)

  const rootRef = useTemplateRef('rootRef')
  const isHovered = useElementHover(rootRef)

  const inputRef = useTemplateRef('inputRef')
  function getInputElement() {
    if (!inputRef.value) {
      return null
    }
    return inputRef.value.$el as HTMLInputElement
  }

  // 处理键盘事件
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault()
      send({ type: 'commit' })
    } else if (event.key === 'Escape') {
      event.preventDefault()
      send({ type: 'cancel' })
    }
  }

  watch(
    () => snapshot.value.value,
    async (state) => {
      if (state === 'editing') {
        await nextTick()
        getInputElement()?.focus()
        getInputElement()?.setSelectionRange(0, -1)
      }
    },
  )
</script>

<template>
  <div
    class="-m-4 flex h-6 items-center p-4"
    ref="rootRef"
  >
    <FileTypeImg
      :file-name="rawFile.name"
      class="mr-3 w-5"
    />

    <div
      v-if="snapshot.value === 'reading'"
      class="min-w-0 flex-1"
    >
      <TextEllipsis
        :text="rawFile.name"
        :lines="1"
        :tooltip="{
          title: rawFile.name,
          placement: 'topLeft',
        }"
      />
    </div>

    <!-- 编辑状态 -->
    <div
      v-else
      class="flex min-w-0 flex-1 items-center"
    >
      <AInput
        :value="snapshot.context.value"
        @update:value="(value) => send({ type: 'change', value })"
        size="small"
        @keydown="handleKeydown"
        @blur="send({ type: 'cancel' })"
        ref="inputRef"
      />

      <IconLoading
        v-if="renameStatus === 'pending'"
        class="text-primary animate-spin"
      />
    </div>

    <AButton
      type="text"
      size="small"
      class="ml-1 size-auto! shrink-0 p-1!"
      @click="send({ type: 'edit' })"
      v-if="snapshot.value === 'reading' && isHovered"
    >
      <IconEdit
        v-if="snapshot.value === 'reading'"
        class="text-primary"
      />
    </AButton>
  </div>
</template>
