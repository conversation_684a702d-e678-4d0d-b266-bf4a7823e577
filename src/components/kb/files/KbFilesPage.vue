<script setup lang="ts">
  import { useFuse } from '@vueuse/integrations/useFuse'

  import { kbFilesContextInjectionKey, injectKbDetailContext } from '@/utils/context/kb'
  import type { TableItem } from './table/KbFileTable.vue'

  import IconSearch from '~icons/ant-design/search-outlined'
  import IconPlay from '~icons/lucide/circle-play'
  import IconLoading from '~icons/lucide/loader-circle'
  import IconTrash from '~icons/lucide/trash-2'

  const { rawKb } = injectKbDetailContext()

  const { data: rawFiles } = useQuery({
    queryKey: kbQueryKey.kbFiles(() => rawKb.value?.id ?? ''),
    async queryFn() {
      const { data } = await kbFetcher<{ data: { data: { docs: API.Kb.File[] } } }>(
        `/datasets/${rawKb.value!.id}/documents/`,
        {
          query: {
            page_size: 99999,
          },
        },
      )
      return data.data.docs
    },
    enabled: () => rawKb.value !== undefined,
    refetchInterval: 15_000,
  })

  const filesTransformed = computed<TableItem[]>(
    () =>
      rawFiles.value?.map<TableItem>((file, index) => ({
        id: file.id,
        chunkMethod: file.chunk_method,
        chunkNum: file.chunk_count,
        createTime: new Date(file.create_time),
        rawIndex: index,
      })) ?? [],
  )

  const selectedFiles = ref<string[]>([])
  provide(kbFilesContextInjectionKey, {
    selectedFiles: shallowReadonly(selectedFiles),
    setSelectedFiles(value) {
      selectedFiles.value = value
    },

    rawFiles: computed(() => rawFiles.value ?? []),
  })

  const searchString = ref('')
  const searchStringDebounced = useDebounce(searchString, 200)

  const { results } = useFuse(searchStringDebounced, filesTransformed, {
    fuseOptions: { keys: ['name'], threshold: 0.3 },
  })

  const files = computed(() => {
    if (!searchStringDebounced.value) {
      return filesTransformed.value
    }
    return results.value.map((result) => result.item)
  })

  const isUploadModalOpen = ref(false)

  const queryClient = useQueryClient()

  const { mutate: parseAll, status: parseAllStatus } = useMutation({
    mutationFn() {
      return kbFetcher(`/datasets/${rawKb.value!.id}/parse-tasks/`, {
        method: 'post',
        body: {
          document_ids: selectedFiles.value,
        },
      })
    },
    onSuccess() {
      return queryClient.invalidateQueries({ queryKey: kbQueryKey.kbFiles(rawKb.value!.id) })
    },
  })

  const { mutate: deleteAll, status: deleteAllStatus } = useMutation({
    mutationFn() {
      return kbFetcher(`/datasets/${rawKb.value!.id}/documents/`, {
        method: 'delete',
        body: {
          ids: selectedFiles.value,
        },
      })
    },
  })
</script>

<template>
  <div>
    <div class="flex flex-wrap items-center gap-y-4 px-(--page-px) pt-6">
      <AInput
        class="max-w-[300px] rounded-full!"
        placeholder="搜索模型名称..."
        v-model:value="searchString"
      >
        <template #suffix>
          <IconSearch class="text-foreground-4" />
        </template>
      </AInput>

      <div class="text-foreground-3 ml-4">
        共有
        <span class="text-primary">{{ files.length }}</span>
        个筛选结果
      </div>

      <div class="ml-auto flex min-w-max items-center space-x-5">
        <AButton
          type="primary"
          class="gradient-a-button w-20!"
          @click="isUploadModalOpen = true"
        >
          新建
        </AButton>

        <AButton
          type="primary"
          ghost
          class="outline-a-button flex! items-center gap-2"
          :disabled="selectedFiles.length === 0"
          @click="parseAll()"
        >
          <IconLoading
            v-if="parseAllStatus === 'pending'"
            class="animate-spin"
          />
          <IconPlay v-else />
          解析
        </AButton>

        <AButton
          type="primary"
          ghost
          class="outline-a-button flex! items-center gap-2"
          :disabled="selectedFiles.length === 0"
          @click="deleteAll()"
        >
          <IconLoading
            v-if="deleteAllStatus === 'pending'"
            class="animate-spin"
          />
          <IconTrash v-else />
          删除
        </AButton>
      </div>
    </div>

    <div class="px-(--page-px) py-4">
      <KbFileTable :files="files" />
    </div>

    <KbUploadModal v-model:open="isUploadModalOpen" />
  </div>
</template>
