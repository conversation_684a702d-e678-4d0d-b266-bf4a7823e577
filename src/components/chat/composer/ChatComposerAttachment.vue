<script setup lang="ts">
  import { twMerge } from 'tailwind-merge'
  import type { HTMLAttributes } from 'vue'

  import IconX from '~icons/material-symbols/cancel-rounded'

  export type Props =
    | {
        class?: HTMLAttributes['class']
        name: string
        wordCount: number
        type: 'file'
      }
    | {
        class?: HTMLAttributes['class']
        name: string
        type: 'kb'
      }

  const props = defineProps<Props>()
  const emit = defineEmits<{ remove: [] }>()

  const filenameParts = computed(() => getFilenameParts(props.name))
</script>

<template>
  <div
    :class="
      twMerge(
        'text-foreground-3 relative w-max rounded-[5px] bg-[rgba(71,148,254,0.1)] py-2 pr-3.5 pl-2.5',
        props.class,
      )
    "
  >
    <TextEllipsis
      v-if="props.type === 'kb'"
      :text="props.name"
      :lines="1"
      :tooltip="{ title: props.name, placement: 'top' }"
    />

    <div
      v-else-if="props.type === 'file'"
      class="flex space-x-2.5"
    >
      <FileTypeImg :file-name="props.name" />
      <div class="max-w-[160px] space-y-1">
        <TextEllipsis
          :text="filenameParts.name"
          :lines="1"
          :tooltip="{ title: props.name, placement: 'top' }"
          class="text-xs"
        />

        <div class="flex space-x-2.5 text-[10px]">
          <div>
            {{ filenameParts.ext }}
          </div>
          <div>{{ props.wordCount }}字</div>
        </div>
      </div>
    </div>

    <button
      class="absolute top-0 right-0 translate-x-1/2 -translate-y-1/2 cursor-pointer"
      @click="emit('remove')"
    >
      <IconX class="text-foreground-3 hover:text-foreground-3/80" />
    </button>
  </div>
</template>
