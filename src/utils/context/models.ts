import type { ShallowRef } from 'vue'

type ModelId = string

export const modelsContextInjectionKey = Symbol('models') as InjectionKey<{
  selectedModels: Readonly<ShallowRef<ModelId[]>>
  setSelectedModels(value: ModelId[]): void

  deployFormRawModel: Readonly<ShallowRef<Partial<API.Models.Model>>>
  setDeployFormRawModel(value: Partial<API.Models.Model>): void

  isDeployModalOpen: Readonly<Ref<boolean>>
  setIsDeployModalOpen(value: boolean): void

  formApi: Readonly<Ref<'create' | 'update'>>
  setFormApi(value: 'create' | 'update'): void

  rawModels: Readonly<ShallowRef<API.Models.Model[]>>
}>

export function injectModelsContext() {
  const context = inject(modelsContextInjectionKey)
  if (!context) {
    throw new Error('modelsContext is not provided')
  }
  return context
}
