<script setup lang="ts">
  import type { FormProps } from 'ant-design-vue'
  import type { MutationStatus } from '@tanstack/vue-query'

  import { injectKbDetailContext } from '@/utils/context/kb'

  const props = withDefaults(defineProps<FormProps & { rawFile: API.Kb.File }>(), {
    layout: 'vertical',
  })

  const { rawKb } = injectKbDetailContext()

  const chunkMethod = ref<string>()
  const delimiter = ref<string>()
  const keywordNum = ref<number>()
  const questionNum = ref<number>()

  watch(
    () => props.rawFile.chunk_method,
    (value) => {
      chunkMethod.value = value
    },
    { immediate: true },
  )

  watch(
    () => props.rawFile.parser_config?.delimiter,
    (value) => {
      delimiter.value = value
    },
    { immediate: true },
  )

  watch(
    () => props.rawFile.parser_config?.auto_keywords,
    (value) => {
      keywordNum.value = value ?? 0
    },
    { immediate: true },
  )

  watch(
    () => props.rawFile.parser_config?.auto_questions,
    (value) => {
      questionNum.value = value ?? 0
    },
    { immediate: true },
  )

  const { mutate: updateFile, status } = useMutation({
    mutationFn() {
      return kbFetcher(`/datasets/${rawKb.value!.id}/documents/${props.rawFile.id}`, {
        method: 'put',
        body: {
          chunk_method: chunkMethod.value,
          parser_config: {
            auto_keywords: keywordNum.value,
            auto_questions: questionNum.value,
            delimiter: delimiter.value,
          },
        } as Partial<API.Kb.File>,
      })
    },
  })

  const emit = defineEmits<{ 'update:status': [MutationStatus] }>()
  watchEffect(() => emit('update:status', status.value))
</script>

<template>
  <AForm
    v-bind="props"
    @finish="updateFile()"
  >
    <AFormItem
      required
      label="分块方法"
      name="chunkMethod"
    >
      <ASelect v-model:value="chunkMethod">
        <ASelectOption value="1">方法1</ASelectOption>
        <ASelectOption value="2">方法2</ASelectOption>
      </ASelect>
    </AFormItem>

    <AFormItem
      label="分隔符"
      required
      name="delimiter"
    >
      <AInput v-model:value="delimiter" />
    </AFormItem>

    <AFormItem
      label="关键词数量"
      name="keywordNum"
      required
    >
      <div class="flex gap-4">
        <ASlider
          class="reset-ant-slider grow"
          v-model:value="keywordNum"
        />
        <AFormItemRest>
          <AInputNumber
            class="max-w-15"
            v-model:value="keywordNum"
          />
        </AFormItemRest>
      </div>
    </AFormItem>

    <AFormItem
      label="问题数量"
      name="questionNum"
      required
    >
      <div class="flex gap-4">
        <ASlider
          class="reset-ant-slider grow"
          v-model:value="questionNum"
        />
        <AFormItemRest>
          <AInputNumber
            class="max-w-15"
            v-model:value="questionNum"
          />
        </AFormItemRest>
      </div>
    </AFormItem>
  </AForm>
</template>
