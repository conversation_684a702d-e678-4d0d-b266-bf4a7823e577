<template>
    <a-spin :spinning="spinning">
    <div class="page">
      <!-- 顶部返回 -->
      <div class="page-header">
        <div class="left">
          <div @click="handleBack" class="back-btn">
            <img style="width: 14px;height: 10px;" src="@/assets/image/ArrowLeft.svg"/>
            <span>返回</span>
          </div>
                <span v-if="!editing" @click="startEditing" class="topTitle">{{ title }}</span>
                <input
                  v-else
                  v-model="teachPlanTitle"
                  @blur="saveTitle"
                  ref="inputRef"
                  class="topTitle"
                />

            <div class="date">已保存于 {{ currentTime }}</div>
        </div>
        <div class="right">
          <button class="btn1" @click="saveTemp()">保存加关联</button>
          <button class="btn2" @click="afterSelect()">下一步-输入描述</button>
        </div>
      </div>


        <div class="page-content">
          <!-- 教案编辑区 -->
          <div class="editor">
            <div v-if="nodata" class="no-data">
              <img src="@/assets/image/lessonPlanSquare/nodata.png"/>
              <span>请选择右侧对应模板</span>
            </div>
            <div v-else class="editor-html" v-html="parseMarkdown(content)"></div>
          </div>
          <!-- 模板选择区 -->
          <div class="template-panel">
            <div class="text">
              <span>选择模板</span>
            </div>
            <div class="toolbar">
              <button class="toolbar-button">
                全部
                <CaretDownOutlined />
              </button>
              <button class="toolbar-button">系统内置</button>
              <button class="toolbar-button">
                课程1
                <CaretDownOutlined />
              </button>
              <button class="toolbar-button">
                课程2
                <CaretDownOutlined />
              </button>
            </div>
            <div class="template-grid">
              <div
                v-for="(template, index) in templateItems"
                :key="index"
                :class="['template-item', selectedIndex === index ? 'selected' : '']"
                @click="selectTemplate(index)"
              >
                <div class="template-index" :class="{ active: selectedIndex === index }">
                  {{ (index + 1).toString().padStart(2, '0') }}
                </div>
                <div class="template-card">
                  <div style="width: 100%;height: 100%;background: rgba(255, 255, 255, 1);padding: 15px;overflow: hidden;">
                    <div class="card-html" v-html="parseMarkdown(
                      `- 标题：${template.title ?? '暂无'}\n- 描述：${template.description ?? '暂无'}\n- 模板类型：${template.template_type ?? '暂无'}\n- 适用学科：${template.subject ?? '暂无'}\n- 适用难度：${template.difficulty ?? '暂无'}`
                    ) "></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
  </a-spin>
</template>

<script lang="ts" setup>
import { ref,onMounted,nextTick} from 'vue'
import { useRouter } from 'vue-router'
import { teachPlanTempList, saveTeachPlanTemp } from '@/services/api/LessonPlan'
import { parseMarkdown } from "@/utils/markdownParser";
import { message } from 'ant-design-vue';
import { CaretDownOutlined } from "@ant-design/icons-vue"

const spinning = ref(false)

const router = useRouter()
const handleBack = () => {
  router.back()
}

const title = ref('这是一个可编辑标题')
const teachPlanTitle = ref('')
const editing = ref(false)
const inputRef = ref<HTMLInputElement | null>(null)
const nodata = ref(true)

function startEditing() {
  teachPlanTitle.value = title.value
  editing.value = true
  // 等 DOM 渲染完成后聚焦
  nextTick(() => {
    inputRef.value?.focus()
  })
}

function saveTitle() {
  title.value = teachPlanTitle.value.trim() || title.value
  editing.value = false
}


const currentTime = ref('')

// 格式化当前时间为 YYYY-MM-DD HH:mm:ss
function formatTime(date: Date): string {
  const Y = date.getFullYear()
  const M = String(date.getMonth() + 1).padStart(2, '0')
  const D = String(date.getDate()).padStart(2, '0')
  const h = String(date.getHours()).padStart(2, '0')
  const m = String(date.getMinutes()).padStart(2, '0')
  const s = String(date.getSeconds()).padStart(2, '0')
  return `${Y}-${M}-${D} ${h}:${m}:${s}`
}

const templateItems = ref<any>([])

onMounted(() => {
  getTeachPlanTempList()
})

// 获取全部教案列表
async function getTeachPlanTempList () {
  const params = {
    is_deleted:'false'
  }
  try {
    spinning.value = true
    const res = await teachPlanTempList(params)
    console.log('获取模板列表:', res.data);
    
    for(let item of res.data){
      templateItems.value.push(item)  
    }
    
    // 移除空字符串和null值
    // templateItems.value = templateItems.value.filter(item => item !== "" && item !== null && item !== undefined)

    // content.value = templateItems.value[selectedIndex.value].content ?? '暂无'
    // const match = content.value.match(/^# (.+)$/m);
    // title.value = match ? match[1] : '无标题';
    
    spinning.value = false
  } catch (error) {
    console.error('获取模板列表失败:', error)
  }
}

const selectedIndex = ref(0)
const content = ref('')

function selectTemplate(index: number) {
  nodata.value = false
  selectedIndex.value = index
  content.value = templateItems.value[selectedIndex.value].content ?? '暂无'
  const match = content.value.match(/^# (.+)$/m);
  title.value = match ? match[1] : '无标题';
}

const saveTemp = async() => {

  const params = {
      title: title.value,
      content: content.value.replace(/^# (.+)$/m, `# ${title.value}\n`),
  }
  try {
    spinning.value = true
    const res = await saveTeachPlanTemp(params)
    spinning.value = false
    message.success('保存成功！')
    currentTime.value = formatTime(new Date())
  } catch (error) {
    console.error('获取PPT列表失败:', error)
  }
}

const afterSelect = () => {
  router.push({
      path:'/LessonPlan/afterSelectTemp',
      state: {
        content: content.value
      }
    })
}


</script>

<style scoped lang="scss">

.page {
  width: 100%;
  min-width: 800px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden; 

  .page-header{
    position: relative; /* 创建层叠上下文 */
    z-index: 1; /* 确保阴影在上层 */
    min-width: 800px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    box-shadow: 0px 2px 10px  rgba(67, 143, 254, 0.1);
    padding: 0 20px;

    .left {
      display: flex;
      align-items: center;
      gap:20px
    }
    .right {
      display: flex;
      align-items: center;
      gap:10px
    }

    .back-btn {
      cursor: pointer;
      // margin-right: 20px;
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .topTitle {
      color: #333;
      // width: 300px;
      font-size: 20px;
      font-weight: 700;
      white-space: nowrap;      /* 不换行 */
      overflow-x: auto;         /* 超出时显示横向滚动条 */  
    }

    .date {
      color: gray;
    }

    .btn1{
      width: 95px;
      height: 32px;
      background: white;
      padding: 10px;
      border-radius: 130px;
      border: 1px solid rgba(229, 229, 229, 1);
      display: inline-flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      font-size: 14px;
      font-weight: 500;
      letter-spacing: 0px;
      line-height: 14px;
      color: rgba(102, 102, 102, 1);
    }

    .btn2{
      width: 143px;
      height: 32px;
      background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
      color: white;
      padding: 10px;
      border-radius: 100px;
      border: none;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      font-size: 14px;
      font-weight: 700;
      letter-spacing: 0px;
      line-height: 14px;
    }
  }

  .page-content {
    flex:1;
    height: 100%;
    min-width: 800px;
    background: url(@/assets/image/bg1.png) no-repeat center;
    background-size: cover;
    padding: 30px;
    display: flex;
    gap:20px;
    // align-items: center;
    justify-content: center;
    
    .editor {
      width: 900px;
      min-width: 600px;
      max-height: 900px;
      background: white;
      padding: 24px 80px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      overflow: auto;
      .editor-html{
        line-height: 50px;
      }
      .no-data {
        height: 100%;
        text-align: center;
        display: flex; 
        flex-direction: column;
        align-items: center; 
        justify-content: center;

        img {
          width: 200px;
        }

        .text {
          color: #999999;
        }
      }
    }

    .template-panel {
      max-height: 900px;
      width: 453px;
      background: white;
      padding: 16px;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      .text {
        font-size: 16px;
        font-weight: 700;
        letter-spacing: 0px;
        line-height: 16px;
        color: rgba(51, 51, 51, 1);
      }

      .toolbar {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid rgba(229, 229, 229, 1);
        gap:16px
      }

      .toolbar-button{
        min-width: 94px;
        height: 32px;
        color: #3f8cff; 
        border: 1px solid #3f8cff;
        border-radius: 5px;
        padding: 10px;
        font-size: 14px;
        font-weight: 500;
        background: rgba(255, 255, 255, 1);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .img{
          color:#3f8cff;
        }
      }
      .toolbar-button:hover {
        background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
        color: #ffffff;
      }

      .template-grid {
        max-height: 800px;
        flex-wrap: wrap; /* 允许子项换行 */
        display: flex;
        // grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        overflow: auto;
        padding-top: 12px;
      }

      .template-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        cursor: pointer;
        user-select: none;
      }

      .template-index {
        font-weight: bold;
        font-size: 18px;
        color: #666;
        width: 28px;
        flex-shrink: 0;
      }

      .template-index.active {
        color: #409EFF;
      }

      .template-card {
        width: 150px;
        height: 180px;
        border-radius: 5px;
        background: rgba(232, 241, 255, 1);
        box-shadow: 0px 2px 10px  rgba(67, 143, 254, 0.1);
        border-radius: 8px;
        padding: 12px;
      }

      .template-item.selected .template-card {
        background-color: rgba(201, 223, 255, 1);
      }

      .card-html {
        width: 100%;
        height: 100%;
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
        color: #333;
        overflow: hidden;
      }
    }

  }


}
</style>
