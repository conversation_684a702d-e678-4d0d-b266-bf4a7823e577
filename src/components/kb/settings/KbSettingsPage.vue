<script lang="ts">
  export interface FormState {
    name: string
    description: string
    embeddingModel: string
    chunkMethod: string
    delimiter: string
    keywordNum: number
    questionNum: number
    courses: string[]
  }

  const kbSettingsPageInjectionKey = Symbol('kb-settings-page') as InjectionKey<{
    formState: Ref<FormState>
  }>

  export function injectKbSettingsPageContext() {
    const context = inject(kbSettingsPageInjectionKey)
    if (!context) {
      throw new Error('kbSettingsPageContext is not provided')
    }
    return context
  }
</script>

<script setup lang="ts">
  import { message } from 'ant-design-vue'

  import { injectKbDetailContext } from '@/utils/context/kb'

  const { rawKb } = injectKbDetailContext()

  const formState = ref<FormState>({
    name: rawKb.value?.name ?? '',
    description: rawKb.value?.description ?? '',
    embeddingModel: rawKb.value?.embedding_model ?? '',
    chunkMethod: rawKb.value?.chunk_method ?? '',
    delimiter: rawKb.value?.parser_config?.delimiter ?? '',
    keywordNum: rawKb.value?.parser_config?.auto_keywords ?? 0,
    questionNum: rawKb.value?.parser_config?.auto_questions ?? 0,
    courses: [],
  })

  provide(kbSettingsPageInjectionKey, {
    formState,
  })

  const { mutate: updateKb, status } = useMutation({
    mutationFn() {
      return kbFetcher(`/datasets/${rawKb.value!.id}/`, {
        method: 'put',
        body: {
          name: formState.value.name,
          description: formState.value.description,
          embedding_model: formState.value.embeddingModel,
          chunk_method: formState.value.chunkMethod,
          parser_config: {
            delimiter: formState.value.delimiter,
            auto_keywords: formState.value.keywordNum,
            auto_questions: formState.value.questionNum,
          },
        } as Partial<API.Kb.Kb>,
      })
    },
    onError(err) {
      message.error(err.message)
    },
  })
</script>

<template>
  <div class="px-(--page-px) py-5">
    <AForm
      id="kb-settings-form"
      class="rounded-md bg-white"
      layout="vertical"
      :model="formState"
      @finish="updateKb()"
    >
      <div class="border-separator border-b border-dashed shadow-[0_2px_19px_#1D4F99C] lg:flex">
        <div
          class="border-separator flex-3 border-b border-dashed px-10 py-7.5 lg:border-r lg:border-b-0 2xl:flex-2"
        >
          <slot name="basic-settings" />
        </div>
        <div class="flex-2 px-10 py-7.5">
          <slot name="chunk-settings" />
        </div>
      </div>

      <div class="flex px-10 py-6">
        <AButton
          class="gradient-a-button ml-auto"
          type="primary"
          html-type="submit"
          form="kb-settings-form"
          :loading="status === 'pending'"
        >
          保存设置
        </AButton>
      </div>
    </AForm>
  </div>
</template>
