<template>
    <div class="page-content">
        <div class="">
            <back :url="'-1'"></back>
            <div class=" flex justify-between items-center">
                <div class="text-[24px] leading-[24px] font-bold my-[20px]">计算机网络-我的题库</div>
                <div class="flex items-center gap-[14px]">
                    <a-button type="primary" class="addbut" @click="addhandleQuetion">添加试题</a-button>
                    <a-button type="primary" class="addbut" @click="addAIQuetion">AI生题</a-button>
                </div>
            </div>
            <div class="quetion">
                <div class="search">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-5 flex-wrap">
                            <a-select v-model:value="course" style="width: 170px;" size="small" :options="optionsCourse"
                                placeholder="关联课程" clearIcon></a-select>
                            <a-select v-model:value="examUser" style="width: 170px;" size="small" :options="optionsUser"
                                placeholder="归属人"></a-select>
                            <a-date-picker show-time v-model:value="params.created_at" placeholder="创建时间" style="width: 237px;" size="small"
                                format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
                                @change="getquestionList" />

                            <a-select :disabled="course == 5" v-model:value="konwledge" mode="multiple"
                                style="width: 356px" placeholder="知识点" size="small"
                                :options="[...Array(10)].map((_, i) => ({ value: '知识点' + (i + 1) }))"
                                clearIcon></a-select>

                            <a-input placeholder="输入关键字" v-model:value="params.stem" size="small" style="width: 258px;" @pressEnter="getquestionList">
                                <template #suffix>
                                    <SearchOutlined style="color: #C4C4C4;" @click="getquestionList" />
                                </template>
                            </a-input>
                        </div>

                    </div>
                    <div class="flex align-center" style="margin-top: 17px;">
                        <div style="width: 42px;">题型：</div>
                        <div>
                            <a-radio-group v-model:value="params.type" @change="changeRadio">
                                <a-radio :value="''">全选</a-radio>
                                <a-radio :value="'单选题'">单选题</a-radio>
                                <a-radio :value="'多选题'">多选题</a-radio>
                                <a-radio :value="'填空题'">填空题</a-radio>
                                <a-radio :value="'判断题'">判断题</a-radio>
                                <a-radio :value="'问答题'">问答题</a-radio>
                            </a-radio-group>
                        </div>
                    </div>
                </div>
                <div v-if="questionLists.length > 0">
                    <div class="flex justify-between items-center">
                        <div class="flex gap-[20px]">
                            <a-checkbox v-model:checked="checkedAll" style="margin-left: 10px;">全选</a-checkbox>
                            <a-button type="primary" size="small" ghost @click="awayQuetion">收起题目详情</a-button>
                        </div>
                        <div class="flex gap-[16px]">
                            <a-button type="primary" ghost>
                                <div class="flex items-center">
                                    <img src="@/assets/image/img/knowledgeicon.png" alt="" class="w-[14px] h-[14px]">
                                    <div class="ml-[3]">修改关联知识点</div>
                                </div>
                            </a-button>
                            <a-button type="primary" ghost @click="deleteQuestion">
                                <div class="flex items-center">
                                    <img src="@/assets/image/img/blueDel.png" alt="" class="w-[14px] h-[14px]">
                                    <div class="ml-[3]">删除</div>
                                </div>
                            </a-button>
                        </div>
                    </div>
                    <!-- 试题组件 -->
                    <Item :isOpen="isOpen" :questionLists="questionLists" @deleteQue="getquestionList" @editQue="editQuetion" :chooseType="'quetion'"></Item>
                </div>
                <div v-else>
                    <img src="@/assets/image/zanwu/nodata.png" style="width: 309px;height: 301px;margin: auto;" />
                </div>
            </div>
            
            <!-- 分页组件 -->
            <div class="pagination">
                <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total"
                    layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </div>
        </div>

        <!-- 新增试题弹窗 -->
        <addQution :open="openAddQution"  @update:open="val => openAddQution = val" @update:success="getquestionList" />
        <!-- 编辑试题弹窗 -->
        <EditQuetion :open="openEditQution" :quetionDetails="quetionDetails" @update:open="val => openEditQution = val"  @update:success="getquestionList"/>
    </div>
</template>

<script lang="ts" setup>
import { usePagination } from '@/hooks/usePagination'; //解构分页参数
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getquestionList);
import addQution from '@/components/question/addQution.vue';
import EditQuetion from '@/components/question/EditQuetion.vue';
import Item from '@/components/question/Item.vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import Back from '@/components/ui/back.vue'
import { reactive, ref, h, onMounted } from 'vue';
import { questionList } from '@/services/api/question';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
//关联课程
const course = ref(5)
const optionsCourse = reactive([
    { value: 1, label: '课程一' },
    { value: 2, label: '课程二' },
    { value: 5, label: '未关联课程' },
])
const checkedAll = ref(false)

//归属人
const examUser = ref()
const optionsUser = reactive([
    { value: 1, label: '归属人一' },
    { value: 2, label: '归属人二' },
])

//知识点
const handleChange = (value: string[]) => {
    console.log(`selected ${value}`);
};
const konwledge = ref<string>();
//控制选项是否展开isOpen
const isOpen = ref(false)
function awayQuetion() {
    isOpen.value = !isOpen.value
}


//删除题目单一
async function deleteQuestion() {
    const confirmed = await showDeleteConfirm('确定删除本记录吗？删除后无法恢复！');
    if (confirmed) {
        // deleteQuestions()
    }
}

const quetionDetails = ref() //编辑试题的数据
//子组件传过来编辑的数据
function editQuetion(data: any) {
    openEditQution.value = true
    quetionDetails.value = data
}

const openAddQution = ref(false)
const openEditQution = ref(false)
//手动添加试题
function addhandleQuetion() {
    openAddQution.value = true
}
import { useRouter } from 'vue-router'
const router = useRouter()
function addAIQuetion() {
    router.push({
        path: '/question/ailist',
        query: {
            type: 1//从题库进入
        }
    })
}

const params = reactive({
    stem: '',
    type: '',
    created_at: '', 
})
const questionLists = ref([]) //题目列表
async function getquestionList(){
    console.log(params,'搜索参数')
    const param = {
        page: currentPage.value,
        page_size: pageSize.value,
        ...params
    }
    questionList(param).then((res:any) => {
        if (res.code == 200) {
            total.value = res.data.count
            questionLists.value = res.data.results
        }
    }).catch(error => {
        console.error('获取失败:', error)
    })
}
function changeRadio(e: any) {
    console.log('changeRadio',e)
    getquestionList()
}


getquestionList()

</script>

<style scoped lang="scss">
// @import url('@/assets/css/questioncss/questionList.css');
.addbut {
    width: 82px;
    height: 32px;
    // color: #fff;
    background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
    border: none;
}

.page-content {
    background: url(@/assets/image/bg1.png) no-repeat center top;
    background-size: cover;
    box-sizing: border-box;
    padding: 0 40px;
    overflow: auto;
    min-height: 100vh;
}

.quetion {
    min-width: 880px;
    // margin: 20px 0 0;
    border-radius: 8.53px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
    height: calc(100vh - 130px - 32px);
    padding: 20px 20px 150px 20px;
    overflow: auto;
}

.search {
    // height: 97px;
    opacity: 1;
    border-radius: 5px;
    background: rgba(63, 140, 255, 0.03);
    padding: 20px 20px 20px 20px;
    margin-bottom: 20px;
}

.pagination {
  text-align: center;
}

:deep(.el-pager li) {
  min-width: 22px !important;
  height: 22px !important;
}

:deep(.el-pager li.is-active) {
  font-size: 14px;
  color: #ffffff;
  background-color: #3f8cff;
}
:deep(.btn-prev) {
  background-color: transparent !important; /* 完全透明 */
}
:deep(.btn-next) {
  background-color: transparent !important; /* 完全透明 */
}

</style>
