import { env } from '@/../env'

const MAX_AUTH_ATTEMPTS = 3

export const useAuthStore = defineStore(
  'auth',
  () => {
    const authAttempts = ref(0)

    function attemptAuth() {
      authAttempts.value += 1
      const nextUrl = location.href
      location.assign(
        `${env.VITE_API_BASE_URL}/api/v1/cas/login/?next=${encodeURIComponent(nextUrl)}`,
      )
    }

    function resetAuthAttempts() {
      authAttempts.value = 0
    }

    function isAuthAttemptsMaxed() {
      return authAttempts.value >= MAX_AUTH_ATTEMPTS
    }

    return {
      authAttempts,

      attemptAuth,
      resetAuthAttempts,
      isAuthAttemptsMaxed,
    }
  },
  { persist: true },
)
