@layer components {
  .gradient-a-button {
    @apply border-none!;

    &:not(*:disabled) {
      background-image: linear-gradient(135.84deg, #219fffff 0%, #0066ffff 100%) !important;
      &:hover {
        background-image:
          linear-gradient(0deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.3)),
          linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%) !important;
      }
    }
  }

  .outline-a-button {
    @apply bg-white!;

    &:not(*:disabled) {
      &:hover {
        background-color: #d1e3ffff !important;
      }
    }
  }

  .icon-a-button {
    @apply text-primary! size-auto! p-1!;
  }

  .reset-ant-table-pagination {
    .ant-pagination {
      @apply my-5! space-x-2! **:border-none!;

      .ant-pagination-item-link {
        @apply rounded-xs!;
      }

      .ant-pagination-item {
        @apply rounded-xs!;

        a {
          @apply flex! size-6! items-center justify-center;
        }

        &:not(.ant-pagination-item-active) {
          a {
            @apply text-foreground-3!;
          }
        }
        &.ant-pagination-item-active {
          @apply bg-primary!;
          a {
            @apply text-white!;
          }
        }
      }
    }
  }

  .reset-ant-modal {
    .ant-modal-header {
      @apply m-0! pb-5!;
    }

    .ant-modal-content {
      @apply px-[30px]!;
    }

    .ant-modal-footer {
      @apply mt-[30px]!;
    }

    .ant-modal-body {
      @apply -mr-[30px]! pt-[10px]! pr-[30px]!;
    }
  }

  .reset-ant-slider {
    .ant-slider-track {
      @apply bg-primary/70!;
    }

    &:hover {
      .ant-slider-track {
        @apply bg-primary!;
      }
    }
  }
}
