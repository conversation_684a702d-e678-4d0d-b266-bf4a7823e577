<template>
  <div class="class-manager">
    <div class="sidebar">
      <ClassList
        :classes="classes"
        v-model:selectedClass="selectedClass"
        @add-class="handleAddClass"
        @settings-class="handleSetClass"
        @delete-class="handleDeleteClass"
      />
    </div>
    <el-dialog v-model="dialogFormVisible" title="新建班级" width="500">
        <el-form ref="ruleNewClassFormRef" :rules="formClassRules" :model="formClass">
          <el-form-item label="班级名称：" prop="name" :label-width="formLabelWidth">
            <el-input v-model="formClass.name" autocomplete="off" />
          </el-form-item>
          <el-form-item label="班级学期：" prop="semester" :label-width="formLabelWidth">
            <el-select v-model="formClass.semester">
              <el-option label="Zone No.1" value="shanghai" />
              <el-option label="Zone No.2" value="beijing" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancelHandleNewClass()">取 消</el-button>
            <el-button type="primary" @click="handleNewClass(ruleNewClassFormRef)">
              确 定
            </el-button>
          </div>
        </template>
    </el-dialog>

    <el-dialog v-model="dialogClsSetFormVisible" title="班级设置" width="500">
        <el-form ref="ruleSetClassFormRef" :rules="formClassRules" :model="formClassSettings">
          <el-form-item label="班级名称：" prop="name" :label-width="formLabelWidth">
            <el-input v-model="formClassSettings.name" autocomplete="off" />
          </el-form-item>
          <el-form-item label="班级学期：" prop="semester" :label-width="formLabelWidth">
            <el-select v-model="formClassSettings.semester">
              <el-option label="Term No.1" value="第一学期" />
              <el-option label="Term No.2" value="第二学期" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancelSetClass()">取 消</el-button>
            <el-button type="primary" @click="handleSettingsClass(ruleSetClassFormRef)">
              确 定
            </el-button>
          </div>
        </template>
    </el-dialog>

    <el-dialog
      v-model="deleteClassDialogVisible"
      title="提示"
      width="400"
      align-center
    >
      <el-icon style="font-size: 18px; color: #f6be18;"><Warning /></el-icon>
      <span style="padding: 0 5px;">确定删除该班级记录吗？删除后无法恢复！</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="deleteClassDialogVisible = false">取 消</el-button>
          <el-button type="danger" size="small" @click="deleteClassDialogVisible = false">
            删 除
          </el-button>
        </div>
      </template>
    </el-dialog>

    <div class="main-content">
      <div class="header-title">
        <el-icon><Management /></el-icon>
        <span>{{ 22 }}级计算机科学与技术</span>
      </div>
      <div class="header">
        <div>
            <el-input
                v-model="searchValue"
                style="width: 280px;min-height: 30px;margin: 0 16px 0 10px;"
                placeholder="请输入"
                :suffix-icon="Search"
            />
            共有{{ 0 }}个筛选结果
        </div>
        <div>
          <el-button style="margin-right: 10px;" size="small" :icon="Delete" :disabled="!selectedRows.length" @click="handleRemove">移除</el-button>
          <el-dropdown placement="bottom-start">
            <el-button type="primary" size="small">添加学生</el-button>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item :icon="Document" @click="handleManualImport">手动导入</el-dropdown-item>
                </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-dialog v-model="dialogImportFormVisible" title="手动导入学生" width="500">
            <el-form :model="formImport" :rules="importRules" ref="importFormRef">
              <el-form-item label="导入学号" prop="studentId" :label-width="formLabelWidth">
                <el-input type="textarea" :rows="10" v-model="formImport.studentId" autocomplete="off" />
                <el-icon><Warning /></el-icon>
                <span>每个学号一行，可多行输入</span>
              </el-form-item>
            </el-form>
            <template #footer>
              <div class="dialog-footer">
                <el-button size="small" @click="dialogImportFormVisible = false">取 消</el-button>
                <el-button size="small" type="primary" @click="handleImportStudents">
                  确 定
                </el-button>
              </div>
            </template>
          </el-dialog>
        </div>
      </div>
      <StudentTable
        :students="filteredStudents"
        :selectedClass="selectedClass"
        :current-page="currentPage"
        :total="students.length"
        @delete-student="handleDeleteStudent"
        @page-change="handlePageChange"
        @selection-change="handleSelectionChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, watch, computed } from 'vue'
import { Management, Search, Delete, Document, Warning } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import ClassList from './classList.vue'
import StudentTable from './studentTable.vue'

type classType = {
    id: number
    name: string
    semester: string
}
type studentType = {
    id: number
    name: string 
    studentId: string
    department: string
    major: string
}

// 状态定义
const dialogFormVisible = ref(false) // 新建班级弹框
const dialogClsSetFormVisible = ref(false) // 班级设置弹框
const deleteClassDialogVisible = ref(false) // 是否确定删除班级弹框
const dialogImportFormVisible = ref(false) // 手动导入学生学号弹框
const searchValue = ref('') // 搜索
const selectedRows = ref<any[]>([])
const currentPage = ref(1)

// 表单数据
const formClass = reactive({
  name: '',
  semester: ''
})
const formClassSettings = reactive({
  name: '',
  semester: ''
})
const formImport = reactive({
  studentId: ''
})

// 班级数据
const classes = ref<classType[]>([])
// 班级学生列表数据
const students = ref<studentType[]>([])

const selectedClass = ref<classType | null>(classes.value[0])

// 计算属性：筛选学生
const filteredStudents = computed(() => {
  if (!searchValue.value) return students.value
  return students.value.filter(student =>
    student.name.includes(searchValue.value) ||
    student.studentId.includes(searchValue.value)
  )
})

// 表单验证规则
const formLabelWidth = '140px'
interface RuleForm {
  name: string
  semester: string
}
const formClassRules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: '请输入班级名称', trigger: 'blur' },
    { min:2, max: 20, message: '长度在2-20之间', trigger: 'blur' },
  ],
  semester: [
    { required: true, message: '请选择班级学期', trigger: 'blur' }
  ]
})
// 表单验证规则——导入学号
const importRules = reactive<FormRules>({
  studentId: [
    { required: true, message: '请输入学号', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入学号'))
          return
        }
        // 验证长度
        if (value.length > 1000) {
          callback(new Error('输入内容不能超过1000个字符'))
          return
        }
        // 验证字符类型（只允许数字、逗号和回车）
        const invalidChars = value.match(/[^0-9,\n\r]/g)
        if (invalidChars) {
          callback(new Error(`包含非法字符：${invalidChars.join('')}`))
          return
        }
        callback
      },
      trigger: 'blur'
    }
  ]
})

// 表单引用
const ruleNewClassFormRef = ref<FormInstance>() // 新建班级表单
const ruleSetClassFormRef = ref<FormInstance>() // 班级设置表单
const importFormRef = ref<FormInstance>() // 导入学号表单

// 表格选择变更处理
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 删除选中学生
const handleRemove = () => {
  if (selectedRows.value.length === 0) return
  // 调用API删除
  console.log('删除选中的学生：', selectedRows.value.map(row => row.id))
  // 模拟删除操作
  students.value = students.value.filter(
    students => !selectedRows.value.some(row => row.id === students.id)
  )
  selectedRows.value = []
}
// 打开新建班级弹框
function handleAddClass() {
  dialogFormVisible.value = true
}
// 打开班级设置弹框
const handleSetClass = () => {
  if (selectedClass.value) {
    formClassSettings.name = selectedClass.value.name
    formClassSettings.semester = selectedClass.value.semester
    dialogClsSetFormVisible.value = true
  }
}

// 取消新建班级
function cancelHandleNewClass() {
  console.log("取消新建班级", formClass.name, formClass.semester)
  formClass.name = ''
  formClass.semester = ''
  dialogFormVisible.value = false
}

const handleNewClass = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      // 调用新建班级接口新建班级，新建成功后关闭对话框。
      console.log('提交新建班级', formClass)
      handleFetchClasses()
      selectedClass.value = classes.value[0]
      dialogFormVisible.value = false
    } else {
      console.log('验证失败!', fields)
    }
  })
}

// 取消班级设置弹框
function cancelSetClass() {
  console.log("取消班级设置", formClassSettings.name, formClassSettings.semester)
  formClassSettings.name = ''
  formClassSettings.semester = ''
  dialogClsSetFormVisible.value = false
}

// 班级设置
const handleSettingsClass = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      // 调用新建班级接口新建班级，新建成功后关闭对话框。
      console.log('提交新建班级')
      handleFetchClasses()
      selectedClass.value = classes.value[0]
      dialogFormVisible.value = false
    } else {
      console.log('验证失败!', fields)
    }
  })
}

// 单行删除学生逻辑
const handleDeleteStudent = (studentId: number) => {
  // 调用API删除学生
  console.log('删除学生', studentId)
  students.value = students.value.filter(student => student.id !== studentId)
}

// 分页逻辑
function handlePageChange(page: number) {
  currentPage.value = page
}

// 手动添加学生逻辑
function handleManualImport() {
  dialogImportFormVisible.value = true
}
// 处理导入学生
const handleImportStudents = () => {
  importFormRef.value?.validate((valid) => {
    if (valid) {
      // 处理导入逻辑
      const studentIds = formImport.studentId
        .split(/[\n\r,]+/) // 按换行符或逗号分割
        .map(id => id.trim())
        .filter(id => id) // 过滤空字符串
      
      console.log('导入的学号:', studentIds)
      
      // 模拟导入学生
      const newStudents = studentIds.map((id, index) => ({
        id: Date.now() + index,
        name: `学生${id}`,
        studentId: id,
        department: selectedClass.value?.name || '计算机学院',
        major: '计算机科学与技术'
      }))
      
      students.value = [...students.value, ...newStudents]
      dialogImportFormVisible.value = false
      formImport.studentId = ''
    } else {
      console.log('验证失败')
      // return false
    }
  })
}

// 删除班级逻辑
function handleDeleteClass(id: number) {
  deleteClassDialogVisible.value = true
}

// 重置新建班级弹框里的内容【备用】
const resetFormClass = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

function handleFetchClasses() {
    // 预留组件加载时调用接口获取班级列表函数。
    classes.value = []
    classes.value.push({ id: 1, name: '22级计算机科学与技术', semester: '第一学期' })
    classes.value.push({ id: 2, name: '22级计算机应用技术', semester: '第二学期' })
    if (classes.value.length > 0) {
        console.log("debug")
        selectedClass.value = classes.value[0]
    }
}

// 班级学生列表部分
function handleFetchClassStudents() {
    // 调用接口获取班级学生列表。
    students.value = []
    if (selectedClass.value) {
        if (selectedClass.value.name === "22级计算机科学与技术") {
            students.value.push({ id: 1, name: '张一', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 2, name: '王', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 3, name: '李', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 4, name: '唐', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 5, name: '李', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 6, name: '阳', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 7, name: '格', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 8, name: '术', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 9, name: '计', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 10, name: '王', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术'})
        } else if (selectedClass.value.name === "22级计算机应用技术") {
            students.value.push({ id: 1, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 2, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 3, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 4, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 5, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 6, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 7, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 8, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 9, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 10, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术'})
        }
    }
}

watch(selectedClass, (newValue, oldValue) => {
    handleFetchClassStudents()
});

// 生命周期钩子
onMounted(() => {
    handleFetchClasses()
    handleFetchClassStudents()
})

</script>

<style scoped>
.class-manager {
  display: flex;
  max-height: 65vh;
}
.sidebar {
  width: 25%;
  background: #ffffff;
}
.main-content {
  flex: 1;
  padding: 0 5px 0 5px;
  margin: 5px;
  border-left: 1px solid #999;
  max-width: 100%;
  width: 75%;
  overflow: auto;
}
.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 8px 8px 8px;
  font-weight: bold;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
</style> 