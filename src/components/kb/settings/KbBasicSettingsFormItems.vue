<script setup lang="ts">
  import { injectKbDetailContext } from '@/utils/context/kb'

  const { rawKb } = injectKbDetailContext()

  const name = ref<string>()
  const description = ref<string>()
  const embeddingModel = ref<string>()
  const courses = ref<string[]>()

  watch(
    () => rawKb.value?.name,
    (value) => {
      name.value = value
    },
    { immediate: true },
  )
  watch(
    () => rawKb.value?.description,
    (value) => {
      description.value = value
    },
    { immediate: true },
  )
  watch(
    () => rawKb.value?.embedding_model,
    (value) => {
      embeddingModel.value = value
    },
    { immediate: true },
  )

  const { data: embeddingModels, status: embeddingModelsStatus } = useQuery({
    queryKey: modelsQueryKey.modelList(['embedding']),
    async queryFn() {
      const { items } = await modelsFetcher<{ items: API.Models.Model[] }>('/models/', {
        query: {
          categories: ['embedding'],
        },
      })
      return items
    },
  })
</script>

<template>
  <div class="space-y-5!">
    <div class="text-foreground-2 text-lg font-bold">基本设置</div>

    <AFormItem
      required
      label="知识库名称"
    >
      <AInput
        v-model:value="name"
        name="name"
      />
    </AFormItem>

    <AFormItem label="知识库描述">
      <ATextarea
        v-model:value="description"
        name="description"
      />
    </AFormItem>

    <AFormItem
      label="嵌入模型"
      required
    >
      <ASelect
        v-model:value="embeddingModel"
        name="embeddingModel"
        :loading="embeddingModelsStatus === 'pending'"
      >
        <ASelectOption
          v-for="model in embeddingModels"
          :key="model.id"
          :value="model.name"
        >
          {{ model.name }}
        </ASelectOption>
      </ASelect>
    </AFormItem>

    <AppPermissionGuard :roles="['teacher']">
      <AFormItem label="关联课程">
        <ASelect
          v-model:value="courses"
          name="courses"
          mode="multiple"
        >
          <ASelectOption value="1">课程1</ASelectOption>
          <ASelectOption value="2">课程2</ASelectOption>
        </ASelect>
      </AFormItem>
    </AppPermissionGuard>
  </div>
</template>
